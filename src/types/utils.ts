/**
 * An initializer function that returns a value of type T.
 */
export type InitializerUpdate<T, ReturnType = T> = (value: T) => ReturnType;

/**
 * An initializer value of function that returns a value of type T. This is the same type as the
 * first argument used by the `useState` hook.
 */
export type Initializer<T, ReturnType = T> = T | InitializerUpdate<T, ReturnType>;

export type InitializerOnChangeProps<T> = {
  onChange: (value: Initializer<T>) => void;
};

/**
 * Like Partial<>, but for deeply nested objects.
 */
export type DeepPartial<T> =
  T extends Record<string, unknown>
    ? {
        [P in keyof T]?: DeepPartial<T[P]>;
      }
    : T;

/**
 * Like Partial<>, but also adds the `undefined` type to each property to allow exactOptionalPropertyTypes
 */
export type PartialUndefined<T> = {
  [P in keyof T]?: T[P] | undefined;
};

type UnionKeys<T> = T extends T ? keyof T : never;
type Expand<T> = T extends T ? {[K in keyof T]: T[K]} : never;

/**
 * An `exclusive or` operator between multiple types. Marks the other properties of the other types as undefined.
 *
 * You can use {@link XOR} if you simply want this utility for two types.
 */
export type OneOf<T extends Record<string, unknown>[]> = {
  [K in keyof T]: Expand<T[K] & Partial<Record<Exclude<UnionKeys<T[number]>, keyof T[K]>, never>>>;
}[number];

/**
 * Creates a union type between the types within the union such that the types are mutually exclusive, and hence,
 * can be used to discriminate between the types.
 *
 * For example, if you have
 * ```ts
 * type A = {a: string};
 * type B = {b: string};
 *
 * function length(arg: A | B) {
 *   return arg.a.length; // Property 'a' does not exist on type 'AorB'.
 *                        //   Property 'a' does not exist on type 'B'.
 * }
 * ```
 * So you can use this type to create the desired effect here, allowing 'a' and 'b' properties to be undefined, and
 * if you check one of th properties using a type guard, deterministically know what type you are dealing with.
 * ```ts
 * type A = {a: string};
 * type B = {b: string};
 *
 * function length(arg: XOR<A, B>) {
 *   if (arg.a) {
 *     return arg.a.length;
 *   }
 *   return arg.b.length
 * }
 * ```
 */
type Without<T, U> = Partial<Record<Exclude<keyof T, keyof U>, never>>;
export type XOR<T, U> = T | U extends object ? (Without<T, U> & U) | (Without<U, T> & T) : T | U;
/**
 * If the generic is undefined, then it will be optional (and can be void), otherwise it will be required.
 */
export type OptionalIfUndefined<T> = T extends undefined ? T | void : T;

/**
 * A generic type of constant/static enum objects.
 */
type EnumObject = Record<string, number | string>;
/**
 * The type of the values of a constant/static enum object. Like "keyof" but for the values.
 */
export type EnumObjectValues<E extends EnumObject> =
  E extends Record<string, infer ET | string> ? ET : never;

export type EnforceEnumKeysInMap<E extends string | number, M extends Record<E, unknown>> = M;

export type SingleOrArray<T> = T | T[] | readonly T[];
