import {useEffect, useState} from 'react';
import {Box, Button, Text, TextLink} from '@base-components';
import {CONTENT_CODES} from '@constants';
import {useAppUserUpdatePrivacyPolicy} from '@contexts';
import {useBooleanDelay, useOnMount} from '@hooks';
import {onLinkToHelp, onLinkToPrivacyPolicy} from '@navigation';

type PrivacyPolicyConfirmationProps = Record<string, unknown>;

export const PrivacyPolicyConfirmation: React.FC<PrivacyPolicyConfirmationProps> = () => {
  const {hasAcceptedPrivacyPolicy, isPendingAccept, isPendingLogout, onAccept, onDismiss} =
    useAppUserUpdatePrivacyPolicy();
  const [hasClickedLink, setHasClickedLink] = useState(Boolean(hasAcceptedPrivacyPolicy));
  const {hasBeenDelayed, startDelayTimer} = useBooleanDelay(3000);
  useOnMount(startDelayTimer);
  useEffect(() => {
    if (!hasBeenDelayed) return;
    setHasClickedLink(true);
  }, [hasBeenDelayed]);

  return (
    <Box>
      <Text variant='headlineMedium'>{CONTENT_CODES().PRIVACY_POLICY_CONFIRMATION.TITLE}</Text>
      <Box display='flex'>
        <Text pb={2} variant='titleLarge'>
          {CONTENT_CODES().PRIVACY_POLICY_CONFIRMATION.WELCOME}
        </Text>
        <Text pb={2}>{CONTENT_CODES().PRIVACY_POLICY_CONFIRMATION.BODY_1}</Text>
        <Text pb={2}>
          {CONTENT_CODES().PRIVACY_POLICY_CONFIRMATION.BODY_2}
          <TextLink onPress={onLinkToHelp}>
            {CONTENT_CODES().PRIVACY_POLICY_CONFIRMATION.CONTACT_EMAIL}
          </TextLink>
          .
        </Text>

        <Button
          contentStyle={{flexDirection: 'row-reverse'}}
          icon='link'
          mode='outlined'
          onPress={() => {
            setHasClickedLink(true);
            onLinkToPrivacyPolicy();
          }}
        >
          {CONTENT_CODES().PRIVACY_POLICY_CONFIRMATION.VIEW_PRIVACY_POLICY_BUTTON_LABEL}
        </Button>

        <Text py={2}>{CONTENT_CODES().PRIVACY_POLICY_CONFIRMATION.NOT_AGREE}</Text>
      </Box>
      <Box flexDirection='row' justifyContent='flex-end'>
        <Button
          contentStyle={{flexDirection: 'row-reverse'}}
          disabled={isPendingLogout || isPendingAccept}
          icon='close'
          loading={isPendingLogout}
          mode='outlined'
          style={{alignSelf: 'flex-start'}}
          onPress={onDismiss}
        >
          {CONTENT_CODES().PRIVACY_POLICY_CONFIRMATION.CANCEL_BUTTON_LABEL}
        </Button>
        <Button
          contentStyle={{flexDirection: 'row-reverse'}}
          disabled={!hasClickedLink || isPendingAccept || isPendingLogout}
          icon='check'
          loading={isPendingAccept}
          ml={1}
          mode='contained'
          style={{alignSelf: 'flex-start'}}
          onPress={onAccept}
        >
          {CONTENT_CODES().PRIVACY_POLICY_CONFIRMATION.ACCEPT_BUTTON_LABEL}
        </Button>
      </Box>
    </Box>
  );
};
