import {useCallback, useState} from 'react';
import {
  Box,
  Button,
  DropDownSelfContained,
  Icon,
  LoaderSpinner,
  LoadingIndicator,
  Text,
} from '@base-components';
import {CONTENT_CODES} from '@constants';
import {
  useAppUserSafe,
  useChallengeById,
  useChallengeGroup,
  useChallengeGroupChildrenCount,
  useChallengeJoin,
  useGroupsForChallengeLevel,
  useIsUserInChallengeInitial,
} from '@contexts';
import {useBooleanLoadOnce, useValueIsUndefinedDelay} from '@hooks';
import {
  ChallengeJoinOperation,
  type ChallengeJoinResponse,
  getInviteCodePayload,
  type InviteCode,
  InviteCodeActionType,
  type InviteCodeChallengePayload,
  type InviteCodeDocument,
  isGroupsChallenge,
  isParticipantsChallenge,
  isTeamsChallenge,
  patternMatch,
  type UUIDString,
} from '@types';
import {memoComponent} from '@utils';
import {ChallengeInviteAcceptReject} from './ChallengeInviteAcceptReject';

type InviteCodeChallengeProps = {
  inviteCode: InviteCode;
  onDismiss: () => void;
  onGoToChallenge: (challengeId: UUIDString) => void;
  onShowConfetti: () => void;
  payload: InviteCodeChallengePayload;
};

/**
 * Represents all possible states of the InviteCodeChallenge component
 */
type InviteCodeChallengeStatus =
  | 'LOADING'
  | 'NOT_FOUND'
  | 'ALREADY_IN_CHALLENGE'
  | 'TEAM_FULL'
  | 'SELECT_PARENT_GROUP'
  | 'AWAITING_RESPONSE'
  | 'ACCEPTED'
  | 'REJECTED';

// eslint-disable-next-line max-lines-per-function, complexity -- to allow for different loading/conditional states
const InviteCodeChallenge: React.FC<InviteCodeChallengeProps> = ({
  inviteCode,
  onDismiss,
  onGoToChallenge,
  onShowConfetti,
  payload,
}) => {
  const {
    isUndefinedAfterDelay,
    reset,
    value: challenge,
  } = useValueIsUndefinedDelay(useChallengeById(payload.challengeId), 20_000);

  const participantsChallenge =
    challenge && isParticipantsChallenge(challenge) ? challenge : undefined;
  const team = participantsChallenge?.teams?.find(t => t.id === payload.teamId);
  const teamSize = participantsChallenge?.participants.filter(p =>
    payload.teamId ? p.teamId === payload.teamId : false,
  ).length;
  const [selectedParentGroupId, setSelectedParentGroupId] = useState<UUIDString | undefined>(
    undefined,
  );
  const [hasConfirmedParentGroup, setHasConfirmedParentGroup] = useState(false);
  const group = useChallengeGroup(challenge?.id, payload.groupId ?? undefined);
  const groupsChallenge = challenge && isGroupsChallenge(challenge) ? challenge : undefined;
  const groupSize = useChallengeGroupChildrenCount(groupsChallenge, group);
  const {data: parentGroups} = useGroupsForChallengeLevel(
    challenge?.id,
    groupsChallenge ? groupsChallenge.groupLevels.length - 1 : undefined,
  );
  const currentTeamSize =
    challenge &&
    patternMatch(challenge)
      .when(isParticipantsChallenge, () => teamSize)
      .when(isGroupsChallenge, () => groupSize)
      .exhaustive();
  const isTeamOrGroupFull = useBooleanLoadOnce(
    challenge &&
    currentTeamSize !== undefined &&
    challenge.maxTeamSize !== undefined &&
    (!!team || !!group)
      ? currentTeamSize >= challenge.maxTeamSize
      : undefined,
  );

  const teamOrGroupName = challenge
    ? patternMatch(challenge)
        .when(isParticipantsChallenge, () => team?.name)
        .when(isGroupsChallenge, () => group?.name)
        .exhaustive()
    : undefined;

  const [confirmationStatus, setConfirmationStatus] = useState<undefined | 'accept' | 'reject'>(
    undefined,
  );
  const [statusVars, setStatusVars] = useState<ChallengeJoinResponse | undefined>(undefined);
  const {id: userId} = useAppUserSafe();
  const isAlreadyInChallenge = useIsUserInChallengeInitial(payload.challengeId, userId);
  const isLoading =
    !challenge ||
    isAlreadyInChallenge === undefined ||
    (!!groupsChallenge && parentGroups === undefined) ||
    // (!!groupsChallenge && (!!selectedGroupId || parentGroups === undefined)) ||
    (!!groupsChallenge && !!payload.groupId && currentTeamSize === undefined) ||
    (!!participantsChallenge && currentTeamSize === undefined);

  const onDismissInternal = useCallback(() => {
    setConfirmationStatus(undefined);
    reset();
    onDismiss();
  }, [onDismiss, reset]);

  const onSuccess = useCallback(
    (options: ChallengeJoinResponse) => {
      setConfirmationStatus('accept');
      setStatusVars(options);
      onShowConfetti();
    },
    [onShowConfetti],
  );

  const joinMutation = useChallengeJoin(onSuccess);
  const [isPendingCreate, setIsPendingCreate] = useState(false);
  const [isPendingAssign, setIsPendingAssign] = useState(false);

  // Determine the current status based on component state - NOTE: order of checking boolean matters
  const status: InviteCodeChallengeStatus = (() => {
    if (isLoading) {
      return isUndefinedAfterDelay ? 'NOT_FOUND' : 'LOADING';
    }

    if (isAlreadyInChallenge && confirmationStatus === undefined) {
      return 'ALREADY_IN_CHALLENGE';
    }

    if (confirmationStatus === 'accept') {
      return 'ACCEPTED';
    }

    if (confirmationStatus === 'reject') {
      return 'REJECTED';
    }

    if (groupsChallenge && !hasConfirmedParentGroup) {
      return 'SELECT_PARENT_GROUP';
    }

    if (isTeamOrGroupFull) {
      return 'TEAM_FULL';
    }

    return 'AWAITING_RESPONSE';
  })();

  return (
    <>
      <Box alignItems='center' py={2}>
        <Icon name='trophy-outline' size={48} />
        <Text variant='titleMedium'>Challenge Invite</Text>
      </Box>

      {patternMatch(status)
        .with('LOADING', () => <LoaderSpinner size='small' />)
        .with('NOT_FOUND', () => (
          <Box alignItems='center' justifyContent='center'>
            <Text textAlign='center' variant='bodyLarge'>
              {CONTENT_CODES().INVITE_CODES.MODAL.CHALLENGE.NOT_FOUND}
            </Text>
            <Button mode='outlined' mt={2} onPress={onDismissInternal}>
              {CONTENT_CODES().INVITE_CODES.MODAL.CLOSE}
            </Button>
          </Box>
        ))
        .with('ALREADY_IN_CHALLENGE', () => (
          <Box alignItems='center' justifyContent='center'>
            <Text textAlign='center' variant='bodyLarge'>
              {CONTENT_CODES().INVITE_CODES.MODAL.CHALLENGE.ALREADY_PARTICIPANT}
            </Text>
            <Button mode='contained' mt={2} onPress={() => onGoToChallenge(payload.challengeId)}>
              {CONTENT_CODES().INVITE_CODES.MODAL.CHALLENGE.GO_TO_CHALLENGE}
            </Button>
          </Box>
        ))
        .with('TEAM_FULL', () => (
          <Box alignItems='center' justifyContent='center'>
            <Text textAlign='center' variant='bodyLarge'>
              {CONTENT_CODES().INVITE_CODES.MODAL.CHALLENGE.TEAM_FULL}
            </Text>
            <Button mode='outlined' mt={2} onPress={onDismissInternal}>
              {CONTENT_CODES().INVITE_CODES.MODAL.CLOSE}
            </Button>
          </Box>
        ))
        .with('SELECT_PARENT_GROUP', () => (
          <Box alignItems='center' justifyContent='center'>
            <Text pb={2} textAlign='center' variant='bodyLarge'>
              {CONTENT_CODES().INVITE_CODES.MODAL.CHALLENGE.SELECT_PARENT_GROUP(
                groupsChallenge!.challengeName,
                groupsChallenge?.groupLevels.at(-2)?.groupLabel, // parent group level label
              )}
            </Text>
            {!parentGroups && <LoadingIndicator />}
            {parentGroups && (
              <DropDownSelfContained
                list={[
                  ...parentGroups.map(p => ({
                    label: p.name,
                    value: p.id,
                  })),
                  {label: 'None selected', value: ''},
                ]}
                setValue={value => value && setSelectedParentGroupId(value)}
                value={selectedParentGroupId ?? ''}
              />
            )}
            <Button
              disabled={!selectedParentGroupId}
              mode='contained'
              mt={4}
              onPress={() => setHasConfirmedParentGroup(true)}
            >
              {CONTENT_CODES().INVITE_CODES.MODAL.CONTINUE}
            </Button>
          </Box>
        ))
        .with('AWAITING_RESPONSE', () =>
          payload.isPublicInvite &&
          (isGroupsChallenge(challenge!) || isTeamsChallenge(challenge!))
            ? (
                <>
                  <Text textAlign='center' variant='bodyLarge'>
                    {selectedParentGroupId
                      ? CONTENT_CODES().INVITE_CODES.MODAL.CHALLENGE.PUBLIC_INVITE_QUESTION_GROUP(
                          groupsChallenge?.groupLevels.at(-2)?.groupLabel, // parent group level label
                        )
                      : CONTENT_CODES().INVITE_CODES.MODAL.CHALLENGE.PUBLIC_INVITE_QUESTION(
                          challenge.challengeName,
                        )}
                  </Text>

                  <Button
                    disabled={isPendingAssign || isPendingCreate}
                    icon='account-multiple-plus-outline'
                    loading={isPendingCreate}
                    mode='outlined'
                    mt={2}
                    onPress={() => {
                      setIsPendingCreate(true);
                      joinMutation.mutate({
                        userId,
                        inviteCode,
                        groupId: payload.groupId ?? undefined,
                        teamId: payload.teamId ?? undefined,
                        parentGroupId: selectedParentGroupId,
                        joinOperation: ChallengeJoinOperation.CREATE,
                      }, {
                        onSettled: () => {
                          setIsPendingCreate(false);
                        },
                      });
                    }}
                  >
                    {CONTENT_CODES().INVITE_CODES.MODAL.CHALLENGE.PUBLIC_CREATE_TEAM}
                  </Button>

                  <Button
                    disabled={isPendingAssign || isPendingCreate}
                    icon='account-group-outline'
                    loading={isPendingAssign}
                    mode='outlined'
                    mt={2}
                    onPress={() => {
                      setIsPendingAssign(true);
                      joinMutation.mutate({
                        userId,
                        inviteCode,
                        groupId: payload.groupId ?? undefined,
                        teamId: payload.teamId ?? undefined,
                        parentGroupId: selectedParentGroupId,
                        joinOperation: ChallengeJoinOperation.ASSIGN,
                      }, {
                        onSettled: () => {
                          setIsPendingAssign(false);
                        },
                      });
                    }}
                  >
                    {CONTENT_CODES().INVITE_CODES.MODAL.CHALLENGE.PUBLIC_JOIN_TEAM}
                  </Button>
                </>
              )
            : (
                <>
                  <Text variant='bodyLarge'>
                    {CONTENT_CODES().INVITE_CODES.MODAL.CHALLENGE.CONFIRMATION_QUESTION(
                      challenge!.challengeName,
                      teamOrGroupName,
                    )}
                  </Text>

                  <Box pt={2}>
                    <ChallengeInviteAcceptReject
                      challenge={challenge!}
                      group={group}
                      team={team}
                      onStatusChange={changes => {
                        setConfirmationStatus(changes.status);
                        if (changes.teamNameJoined) {
                          setStatusVars({teamNameJoined: changes.teamNameJoined});
                        }
                        if (changes.status === 'accept') {
                          onShowConfetti();
                        }
                      }}
                    />
                  </Box>
                </>
              ),
        )
        .with('ACCEPTED', () => {
          const isTeamCaptain = statusVars?.isTeamCaptain;
          const teamNameJoined = statusVars?.teamNameJoined;
          const challengeContent = CONTENT_CODES().INVITE_CODES.MODAL.CHALLENGE;

          // Determine the content for the first Text component
          let primaryTextContent: string;
          if (teamNameJoined) {
            primaryTextContent = isTeamCaptain
              ? challengeContent.CONFIRMATION_ACCEPT_CAPTAIN_TEAM_NAME(teamNameJoined)
              : challengeContent.CONFIRMATION_ACCEPT_TEAM_NAME(teamNameJoined);
          } else {
            primaryTextContent = challengeContent.CONFIRMATION_ACCEPT;
          }

          return (
            <Box alignItems='center' justifyContent='center'>
              <Text textAlign='center' variant='bodyLarge'>
                {primaryTextContent}
              </Text>
              {isTeamCaptain && (
                <Text pt={1} textAlign='center' variant='bodyMedium'>
                  {challengeContent.CONFIRMATION_ACCEPT_CAPTAIN}
                </Text>
              )}

              <Button mode='contained' mt={2} onPress={() => onGoToChallenge(payload.challengeId)}>
                {CONTENT_CODES().INVITE_CODES.MODAL.CHALLENGE.GO_TO_CHALLENGE}
              </Button>
            </Box>
          );
        })
        .with('REJECTED', () => (
          <Box alignItems='center' justifyContent='center'>
            <Text textAlign='center' variant='bodyLarge'>
              {CONTENT_CODES().INVITE_CODES.MODAL.CHALLENGE.CONFIRMATION_REJECT}
            </Text>
            <Button mode='outlined' mt={2} onPress={onDismissInternal}>
              {CONTENT_CODES().INVITE_CODES.MODAL.CLOSE}
            </Button>
          </Box>
        ))
        .exhaustive()}
    </>
  );
};

type InviteCodeContentProps = {
  inviteCodeData: InviteCodeDocument;
  onDismiss: () => void;
  onGoToChallenge: (challengeId: UUIDString) => void;
  onShowConfetti: () => void;
};

/**
 * Component that renders the content of an invite code based on its action type
 */
export const InviteCodeContent = memoComponent<InviteCodeContentProps>(
  ({inviteCodeData, onDismiss, onGoToChallenge, onShowConfetti}) =>
    patternMatch(inviteCodeData.actionType)
      .with(InviteCodeActionType.JOIN_CHALLENGE, () => {
        const payload = getInviteCodePayload(inviteCodeData, InviteCodeActionType.JOIN_CHALLENGE)!;

        return (
          <InviteCodeChallenge
            inviteCode={inviteCodeData.id}
            payload={payload}
            onDismiss={onDismiss}
            onGoToChallenge={onGoToChallenge}
            onShowConfetti={onShowConfetti}
          />
        );
      })
      .otherwise(() => (
        <Box alignItems='center'>
          <Text mt={2}>Unknown invite type. Please contact support for assistance.</Text>
        </Box>
      )),
);
