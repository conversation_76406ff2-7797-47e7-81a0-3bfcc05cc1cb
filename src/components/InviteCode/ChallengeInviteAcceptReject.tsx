import {useCallback} from 'react';
import {Box, Button} from '@base-components';
import {CONTENT_CODES} from '@constants';
import {useAppUserSafe, useChallengeJoin} from '@contexts';
import type {Challenge, ChallengeGroupDocument, ChallengeJoinResponse, ChallengeTeam} from '@types';
import {onHapticSelection, useAppTheme} from '@utils';

type ChallengeInviteAcceptRejectProps = {
  challenge: Challenge;
  group: ChallengeGroupDocument | undefined;
  onStatusChange: (options: {
    status: 'accept' | 'reject';
    teamNameJoined: string | undefined;
  }) => void;
  team: ChallengeTeam | undefined;
};

/**
 * Component that displays accept and reject buttons for a challenge invite
 * Unlike ChallengeAcceptReject, this component handles adding the user to the challenge
 * when they accept the invitation via an invite code
 */
export const ChallengeInviteAcceptReject: React.FC<ChallengeInviteAcceptRejectProps> = ({
  challenge,
  group,
  onStatusChange,
  team,
}) => {
  const {id: userId} = useAppUserSafe();
  const theme = useAppTheme();

  const onSuccess = useCallback((options: ChallengeJoinResponse) => {
    onStatusChange({status: 'accept', teamNameJoined: options.teamNameJoined});
  }, [onStatusChange],
  );
  const {isPending, isSuccess, mutate} = useChallengeJoin(onSuccess);

  return (
    <Box flexDirection='row' justifyContent='center'>
      <Button
        disabled={isPending || isSuccess}
        icon='check-circle-outline'
        loading={isPending}
        mode='outlined'
        textColor={theme.actions.confirmColor}
        onPress={() => {
          void onHapticSelection();
          mutate({
            challengeId: challenge.id,
            userId,
            groupId: group?.id,
            teamId: team?.id,
          });
        }}
      >
        {CONTENT_CODES().CHALLENGE.INVITE.ACCEPT}
      </Button>
      <Button
        disabled={isPending || isSuccess}
        icon='close-circle-outline'
        ml={1}
        mode='outlined'
        textColor={theme.actions.denyColor}
        onPress={() => {
          void onHapticSelection();
          onStatusChange({status: 'reject', teamNameJoined: undefined});
        }}
      >
        {CONTENT_CODES().CHALLENGE.INVITE.REJECT}
      </Button>
    </Box>
  );
};
