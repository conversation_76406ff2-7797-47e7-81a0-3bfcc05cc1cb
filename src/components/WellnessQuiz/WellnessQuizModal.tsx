/* eslint-disable no-nested-ternary -- for chasing colors */
import {useEffect, useMemo, useRef, useState} from 'react';
import {Box, ButtonCentered, Confetti, Icon, Text, TouchableHighlight} from '@base-components';
import {CONTENT_CODES} from '@constants';
import {useAddQuizProgress, useOnDismissStoreRating} from '@contexts';
import type {QuizOption, WellnessQuiz} from '@types';
import {onHapticSelection, useAppTheme} from '@utils';
import {ManagedModal} from '../Shared';

type WellnessQuizModalProps = {
  isAlreadyCompleted?: boolean;
  isOpen: boolean;
  onDismiss: () => void;
  quiz: WellnessQuiz;
};

export const WellnessQuizModal: React.FC<WellnessQuizModalProps> = ({
  isAlreadyCompleted,
  isOpen,
  onDismiss,
  quiz,
}) => {
  const theme = useAppTheme();
  const [answerGuesses, setAnswerGuesses] = useState<QuizOption[]>([]);
  const quizOptionsRandomized = useMemo(
    () => [...quiz.options].sort(() => Math.random() - 0.5),
    [quiz.options],
  );
  const setQuizCompleted = useAddQuizProgress();

  const isCorrect = answerGuesses.some(option => option.isCorrect);
  const isIncorrect = answerGuesses.length > 0 && !isCorrect;

  const hasRanRef = useRef(isAlreadyCompleted ?? false);
  useEffect(() => {
    if (!isAlreadyCompleted) return;
    hasRanRef.current = true;
    const correctAnswer = quiz.options.find(option => option.isCorrect)!;
    setAnswerGuesses(p => [...p, correctAnswer]);
  }, [isAlreadyCompleted, quiz.options]);

  useEffect(() => {
    if (isCorrect && !hasRanRef.current) {
      hasRanRef.current = true;
      void setQuizCompleted(quiz.id);
    }
  }, [isCorrect, quiz.id, setQuizCompleted]);

  const onDismissStoreRating = useOnDismissStoreRating(onDismiss);

  return (
    <ManagedModal
      hasAbsoluteDismissButton
      isFullWidth
      isOpen={isOpen}
      outsideSurfaceElement={isCorrect && <Confetti />}
      onDismiss={onDismiss}
    >
      <Text variant='headlineMedium'>
        <Icon color={theme.colors.quizPrimaryColor} name='calendar-question' size={28} />
        {CONTENT_CODES().WELLNESS_QUIZ.TITLE}
      </Text>

      <Text py={2} variant='headlineSmall'>
        {quiz.question}
      </Text>

      {quizOptionsRandomized.map((option, index) => (
        <Box key={`option-${option.answer}`} py={1}>
          <TouchableHighlight
            onPress={() => {
              if (isCorrect) return;
              setAnswerGuesses(p => [...p, option]);
              void onHapticSelection();
            }}
          >
            <Text
              style={{
                paddingVertical: 8,
                paddingHorizontal: 12,
                height: 'auto',
                lineHeight: 20,
                borderColor: theme.colors.primary,
                borderWidth: 1,
                borderRadius: 20,
                color: answerGuesses.includes(option)
                  ? (option.isCorrect
                      ? theme.colors.quizCorrect
                      : theme.colors.quizIncorrect)
                  : theme.colors.primary,
              }}
              variant='labelLarge'
            >
              {`${index + 1}. `}
              {answerGuesses.includes(option) && option.isCorrect && '✅ '}
              {answerGuesses.includes(option) && !option.isCorrect && '❌ '}
              {option.answer}
            </Text>
          </TouchableHighlight>
        </Box>
      ))}

      <Box pt={2} />

      {isIncorrect && (
        <Text textAlign='center'>{CONTENT_CODES().WELLNESS_QUIZ.INCORRECT_HINT}</Text>
      )}

      {isCorrect && (
        <>
          <Text variant='bodyLarge'>{CONTENT_CODES().WELLNESS_QUIZ.CORRECT}</Text>
          <Text pt={2} variant='bodyMedium'>
            {quiz.explanation ??
              CONTENT_CODES().WELLNESS_QUIZ.DEFAULT_EXPLANATION(answerGuesses.at(-1)?.answer)}
          </Text>
          <ButtonCentered boxProps={{pt: 2}} mode='outlined' onPress={onDismissStoreRating}>
            {CONTENT_CODES().WELLNESS_QUIZ.CLOSE}
          </ButtonCentered>
        </>
      )}
    </ManagedModal>
  );
};
/* eslint-enable  */
