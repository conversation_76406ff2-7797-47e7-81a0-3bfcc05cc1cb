import {openURL} from 'expo-linking';
import {useState} from 'react';
import {Images} from '@assets';
import {
  Box,
  Button,
  Icon,
  LoaderWrapper,
  Text,
  TextLink,
  TouchableHighlight,
} from '@base-components';
import {CONTENT_CODES, isAndroid, isIos} from '@constants';
import {useFitbitConnected, useFitbitLogout, useTrackingDeviceType} from '@contexts';
import {onFitbitAuthorization} from '@navigation';
import {type ChildrenProps, TrackingDeviceTypes} from '@types';
import {ManagedModal} from '../Shared';
import {FitbitDebug} from './FitbitDebug';

type ContentOnlyProps = {
  isContentOnly?: boolean | undefined;
  isShowEmail?: boolean | undefined;
};

type ExplanationPopUpProps = {
  brandName: string;
} & ChildrenProps &
ContentOnlyProps;

const ExplanationPopUp: React.FC<ExplanationPopUpProps> = ({
  brandName,
  children,
  isContentOnly = false,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  if (isContentOnly) return children;

  return (
    <>
      <Button
        icon='information-outline'
        mode='outlined'
        mt={2}
        onPress={() => setIsModalOpen(true)}
      >
        {CONTENT_CODES().SETTINGS.HEALTH_SYNC.CONNECTION_LEARN_MORE(brandName)}
      </Button>
      <ManagedModal isOpen={isModalOpen} onDismiss={() => setIsModalOpen(false)}>
        <Text variant='headlineMedium'>
          <Icon name='information-outline' size={28} />{' '}
          {CONTENT_CODES().SETTINGS.HEALTH_SYNC.CONNECTION_TITLE(brandName)}
        </Text>

        <Box pt={2} />

        {children}

        <Box flexDirection='row' justifyContent='center' pt={4}>
          <Button mode='outlined' onPress={() => setIsModalOpen(false)}>
            {CONTENT_CODES().EXPLANATIONS.TRACKING_DEVICE_POP_UP.CLOSE}
          </Button>
        </Box>
      </ManagedModal>
    </>
  );
};

const FitbitContent: React.FC<ContentOnlyProps> = ({isContentOnly, isShowEmail}) => {
  const isFitbitConnected = useFitbitConnected();
  const {mutate: fitbitLogout} = useFitbitLogout();

  const content = (
    <>
      {isFitbitConnected ? (
        <>
          <Text pb={2}>{CONTENT_CODES().SETTINGS.HEALTH_SYNC.FITBIT_IS_CONNECTED_EXPLANATION}</Text>
          <TextLink
            onPress={() => {
              void openURL(CONTENT_CODES().SETTINGS.HEALTH_SYNC.FITBIT_LEARN_MORE_LINK);
            }}
          >
            {CONTENT_CODES().SETTINGS.HEALTH_SYNC.FITBIT_LEARN_MORE}
          </TextLink>
        </>
      ) : (
        <Text>{CONTENT_CODES().SETTINGS.HEALTH_SYNC.FITBIT_NOT_CONNECTED_INFO}</Text>
      )}
      {isShowEmail && <Text pt={2}>{CONTENT_CODES().SETTINGS.HEALTH_SYNC.FITBIT_EMAIL_INFO}</Text>}
    </>
  );

  if (isContentOnly) return content;

  return (
    <LoaderWrapper isLoading={isFitbitConnected === undefined}>
      {!isFitbitConnected && (
        <Text pt={2}>{CONTENT_CODES().SETTINGS.HEALTH_SYNC.FITBIT_NOT_CONNECTED_INFO}</Text>
      )}
      <Box pt={2} />
      <TouchableHighlight
        onPress={() => {
          void onFitbitAuthorization();
        }}
      >
        <Box
          style={{
            borderRadius: 10,
            backgroundColor: '#fff',
            padding: 8,
            marginBottom: 8,
            alignItems: 'center',
            justifyContent: 'space-between',
            flexDirection: 'row',
          }}
        >
          <Box alignItems='center' flexDirection='row'>
            <Images.fitbitLogo style={{height: 32, width: 123}} />
          </Box>
          <Box py={1}>
            <Text>
              {isFitbitConnected
                ? CONTENT_CODES().SETTINGS.HEALTH_SYNC.FITBIT_CONNECTED_LABEL
                : CONTENT_CODES().SETTINGS.HEALTH_SYNC.FITBIT_NOT_CONNECTED_LABEL}
            </Text>
          </Box>
          <Box py={1}>
            <Icon name='chevron-right' size={32} />
          </Box>
        </Box>
      </TouchableHighlight>
      {isFitbitConnected && (
        <>
          <Text>{CONTENT_CODES().SETTINGS.HEALTH_SYNC.FITBIT_IS_CONNECTED_EXPLANATION}</Text>

          <ExplanationPopUp brandName='Fitbit'>
            <Text variant='titleMedium'>
              {CONTENT_CODES().SETTINGS.HEALTH_SYNC.FITBIT_DATA_SYNC_TITLE}
            </Text>
            <Text>{CONTENT_CODES().SETTINGS.HEALTH_SYNC.FITBIT_DATA_SYNC_1}</Text>
            <Box pt={2} />
            <Text>{CONTENT_CODES().SETTINGS.HEALTH_SYNC.FITBIT_DATA_SYNC_2}</Text>

            <Box pt={2} />
            <Text variant='titleMedium'>
              {CONTENT_CODES().SETTINGS.HEALTH_SYNC.FITBIT_HOW_SYNC_TITLE}
            </Text>
            <Text>{CONTENT_CODES().SETTINGS.HEALTH_SYNC.FITBIT_HOW_SYNC_1}</Text>
            <Box pt={2} />
            <Text>{CONTENT_CODES().SETTINGS.HEALTH_SYNC.FITBIT_HOW_SYNC_STEP_1}</Text>
            <Text>{CONTENT_CODES().SETTINGS.HEALTH_SYNC.FITBIT_HOW_SYNC_STEP_2}</Text>
            <Box pt={2} />
            <Text>{CONTENT_CODES().SETTINGS.HEALTH_SYNC.FITBIT_HOW_SYNC_2}</Text>
          </ExplanationPopUp>

          <Box pt={2}>
            <Button icon='cancel' mode='outlined' onPress={() => fitbitLogout()}>
              {CONTENT_CODES().SETTINGS.HEALTH_SYNC.FITBIT_REMOVE_LABEL}
            </Button>
          </Box>

          <FitbitDebug />
        </>
      )}
    </LoaderWrapper>
  );
};

const AppleContent: React.FC<ContentOnlyProps> = ({isContentOnly, isShowEmail}) => (
  <ExplanationPopUp brandName='Apple' isContentOnly={isContentOnly}>
    <Text pb={2}>{CONTENT_CODES().SETTINGS.HEALTH_SYNC.APPLE_INFO_1}</Text>
    <Text pb={2}>{CONTENT_CODES().SETTINGS.HEALTH_SYNC.APPLE_INFO_2}</Text>
    <TextLink
      onPress={() => {
        void openURL(CONTENT_CODES().SETTINGS.HEALTH_SYNC.APPLE_LEARN_MORE_LINK);
      }}
    >
      {CONTENT_CODES().SETTINGS.HEALTH_SYNC.APPLE_LEARN_MORE}
    </TextLink>
    {isShowEmail && <Text pt={2}>{CONTENT_CODES().SETTINGS.HEALTH_SYNC.APPLE_INFO_EMAIL}</Text>}
  </ExplanationPopUp>
);

const GarminIosContent: React.FC<{isShowEmail: boolean | undefined}> = ({isShowEmail}) => (
  <>
    <Text pb={2}>{CONTENT_CODES().SETTINGS.HEALTH_SYNC.GARMIN_IOS_INFO_1} </Text>
    <TextLink
      onPress={() => {
        void openURL(CONTENT_CODES().SETTINGS.HEALTH_SYNC.GARMIN_IOS_INFO_LINK);
      }}
    >
      {CONTENT_CODES().SETTINGS.HEALTH_SYNC.GARMIN_IOS_INFO_LINK_LABEL}
    </TextLink>
    {isShowEmail && <Text pt={2}>{CONTENT_CODES().SETTINGS.HEALTH_SYNC.GARMIN_IOS_EMAIL} </Text>}
  </>
);

const GarminAndroidContent: React.FC = () => (
  <>
    <Text>{CONTENT_CODES().SETTINGS.HEALTH_SYNC.GARMIN_ANDROID_INFO} </Text>
    <Box pt={2} />
    <TextLink
      onPress={() => {
        void openURL(CONTENT_CODES().SETTINGS.HEALTH_SYNC.GARMIN_ANDROID_INFO_LINK);
      }}
    >
      {CONTENT_CODES().SETTINGS.HEALTH_SYNC.GARMIN_ANDROID_INFO_LINK_LABEL}
    </TextLink>
    <Box pt={2} />
    <Text>{CONTENT_CODES().SETTINGS.HEALTH_SYNC.GARMIN_ANDROID_ALT_INFO} </Text>
    <Box pt={2} />
    <TextLink
      onPress={() => {
        void openURL(CONTENT_CODES().SETTINGS.HEALTH_SYNC.GARMIN_ANDROID_ALT_LINK);
      }}
    >
      {CONTENT_CODES().SETTINGS.HEALTH_SYNC.GARMIN_ANDROID_ALT_LINK_LABEL}
    </TextLink>
  </>
);

const GarminContent: React.FC<ContentOnlyProps> = ({isContentOnly, isShowEmail}) => (
  <ExplanationPopUp brandName='Garmin' isContentOnly={isContentOnly}>
    {isIos && <GarminIosContent isShowEmail={isShowEmail} />}
    {isAndroid && <GarminAndroidContent />}
  </ExplanationPopUp>
);

const SamsungContent: React.FC<ContentOnlyProps> = ({isContentOnly}) => (
  <ExplanationPopUp brandName='Samsung' isContentOnly={isContentOnly}>
    <Text>{CONTENT_CODES().SETTINGS.HEALTH_SYNC.SAMSUNG_HEALTH_INFO} </Text>
    <Box pt={2} />
    <TextLink
      onPress={() => {
        void openURL(CONTENT_CODES().SETTINGS.HEALTH_SYNC.SAMSUNG_HEALTH_LINK);
      }}
    >
      {CONTENT_CODES().SETTINGS.HEALTH_SYNC.SAMSUNG_HEALTH_LINK_LABEL}
    </TextLink>
  </ExplanationPopUp>
);

const WhoopContent: React.FC<ContentOnlyProps> = ({isContentOnly}) => (
  <ExplanationPopUp brandName='WHOOP' isContentOnly={isContentOnly}>
    <Text>{CONTENT_CODES().SETTINGS.HEALTH_SYNC.WHOOP_INFO} </Text>
    <Box pt={2} />
    <TextLink
      onPress={() => {
        void openURL(CONTENT_CODES().SETTINGS.HEALTH_SYNC.WHOOP_LINK);
      }}
    >
      {CONTENT_CODES().SETTINGS.HEALTH_SYNC.WHOOP_LINK_LABEL}
    </TextLink>
  </ExplanationPopUp>
);

const CorosContent: React.FC<ContentOnlyProps> = ({isContentOnly}) => (
  <ExplanationPopUp brandName='Coros' isContentOnly={isContentOnly}>
    <Text>{CONTENT_CODES().SETTINGS.HEALTH_SYNC.COROS_INFO} </Text>
    <Box pt={2} />
    <TextLink
      onPress={() => {
        void openURL(CONTENT_CODES().SETTINGS.HEALTH_SYNC.COROS_LINK);
      }}
    >
      {CONTENT_CODES().SETTINGS.HEALTH_SYNC.COROS_LINK_LABEL}
    </TextLink>
  </ExplanationPopUp>
);

const OuraContent: React.FC<ContentOnlyProps> = ({isContentOnly}) => (
  <ExplanationPopUp brandName='Oura' isContentOnly={isContentOnly}>
    <Text>{CONTENT_CODES().SETTINGS.HEALTH_SYNC.OURA_INFO} </Text>
    <Box pt={2} />
    <TextLink
      onPress={() => {
        void openURL(CONTENT_CODES().SETTINGS.HEALTH_SYNC.OURA_INFO_LINK);
      }}
    >
      {CONTENT_CODES().SETTINGS.HEALTH_SYNC.OURA_INFO_LINK_LABEL}
    </TextLink>
  </ExplanationPopUp>
);

const PhoneContent: React.FC<ContentOnlyProps> = ({isContentOnly}) => (
  <ExplanationPopUp brandName='Phone' isContentOnly={isContentOnly}>
    <Text>{CONTENT_CODES().SETTINGS.HEALTH_SYNC.PHONE_GENERIC_INFO}</Text>
  </ExplanationPopUp>
);

const NoDeviceContent: React.FC<ContentOnlyProps> = ({isContentOnly}) =>
  isContentOnly && (
    <Box>
      <Text>{CONTENT_CODES().SETTINGS.HEALTH_SYNC.NO_DEVICE_CONTENT_1}</Text>
      <Text pt={2}>{CONTENT_CODES().SETTINGS.HEALTH_SYNC.NO_DEVICE_CONTENT_2}</Text>
      <Text pt={2}>{CONTENT_CODES().SETTINGS.HEALTH_SYNC.NO_DEVICE_CONTENT_3}</Text>
    </Box>
  );

const OtherBrandContent: React.FC<ContentOnlyProps> = ({isContentOnly}) => (
  <Box>
    <Text pt={isContentOnly ? 0 : 2}>{CONTENT_CODES().SETTINGS.HEALTH_SYNC.OTHER_INFO}</Text>
  </Box>
);

type TrackingDeviceExplanationsProps = ContentOnlyProps;

export const TrackingDeviceExplanations: React.FC<TrackingDeviceExplanationsProps> = ({
  isContentOnly,
  isShowEmail,
}) => {
  const deviceTrackingType = useTrackingDeviceType();

  if (deviceTrackingType === TrackingDeviceTypes.Fitbit) {
    return <FitbitContent isContentOnly={isContentOnly} isShowEmail={isShowEmail} />;
  }
  if (deviceTrackingType === TrackingDeviceTypes.Apple && isIos) {
    return <AppleContent isContentOnly={isContentOnly} isShowEmail={isShowEmail} />;
  }
  if (deviceTrackingType === TrackingDeviceTypes.Garmin) {
    return <GarminContent isContentOnly={isContentOnly} isShowEmail={isShowEmail} />;
  }
  if (deviceTrackingType === TrackingDeviceTypes.Samsung) {
    return <SamsungContent isContentOnly={isContentOnly} />;
  }
  if (deviceTrackingType === TrackingDeviceTypes.Whoop) {
    return <WhoopContent isContentOnly={isContentOnly} />;
  }
  if (deviceTrackingType === TrackingDeviceTypes.Coros) {
    return <CorosContent isContentOnly={isContentOnly} />;
  }
  if (deviceTrackingType === TrackingDeviceTypes.Oura) {
    return <OuraContent isContentOnly={isContentOnly} />;
  }
  if (deviceTrackingType === TrackingDeviceTypes.Other) {
    return <OtherBrandContent isContentOnly={isContentOnly} />;
  }
  if (deviceTrackingType === TrackingDeviceTypes.Phone && isIos) {
    return <PhoneContent isContentOnly={isContentOnly} />;
  }
  if (
    deviceTrackingType === TrackingDeviceTypes.NoDevice ||
    (deviceTrackingType === TrackingDeviceTypes.Phone && isAndroid)
  ) {
    return <NoDeviceContent isContentOnly={isContentOnly} />;
  }

  return null;
};
