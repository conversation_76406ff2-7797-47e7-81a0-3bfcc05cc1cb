import {openURL} from 'expo-linking';
import {useCallback, useEffect, useMemo, useState} from 'react';
import {
  Box,
  Button,
  Checkbox,
  Grid,
  IconButton,
  PhotoListInput,
  StickyAboveNavigation,
  Switch,
  Text,
  TextInput,
  TextLink,
} from '@base-components';
import {CONTENT_CODES, DEV_FEATURE_FLAGS, DOMAIN_CONSTANTS} from '@constants';
import {useCopyToClipboard, useIsWorkoutTodayOrBefore, useWorkoutState} from '@contexts';
import {useCurrentTimeZoneWithDefault, useDateEveryMinute} from '@hooks';
import {createShareWorkoutLink, shareContent} from '@navigation';
import {allWorkoutTypesToDisplay, getWorkoutTypeLabel, timestampToDate, type Workout} from '@types';
import {
  formatTimestamp,
  getCalendarDaysDifference,
  getIsoStringFromDate,
  getWorkoutDurationDisplay,
  isEmptyArray,
  isValidLink,
  noop,
  useAppTheme,
} from '@utils';
import {CopyClipboardWrapper} from '../CopyClipboardButton';
import {DateAndTimeComponent} from '../DateAndTimeComponent';
import {SearchAndAddUsers} from '../Participants';
import {DuplicateWorkoutButton} from './CreateWorkoutButton';
import {WorkoutLinks} from './WorkoutLinks';
import {WorkoutStatusIcon} from './WorkoutStatusIcon';

type EditWorkoutProps = {
  hasAddParticipants: boolean;
  isFirstCreate?: true;
  isOutline?: true;
  isReadOnly?: true;
  onHasChange?: (value: boolean) => void;
  onSubmitSuccess?: () => void;
  searchQueryKey: string;
  submitText?: string;
  value: Workout;
};

// eslint-disable-next-line max-lines-per-function, complexity -- form component
export const EditWorkout: React.FC<EditWorkoutProps> = ({
  hasAddParticipants,
  isFirstCreate = false,
  isReadOnly = false,
  onHasChange,
  onSubmitSuccess,
  searchQueryKey,
  submitText,
  value,
}) => {
  const {
    isChanged,
    isLoading,
    onAddParticipant,
    onEndDateChange,
    onImagesChanged,
    onIsCompletedChange,
    onLinksChanged,
    onNameChange,
    onNotesChange,
    onRemoveParticipant,
    onStartDateChange,
    onSubmit,
    onTypeChange,
    workout,
  } = useWorkoutState(value, isFirstCreate, onSubmitSuccess);
  useEffect(() => {
    onHasChange?.(isChanged);
  }, [isChanged, onHasChange]);

  const theme = useAppTheme();
  const [hasWorkoutNameChanged, setHasWorkoutNameChanged] = useState(false);
  const [isLinksError, setIsLinksError] = useState(false);
  const copyToClipboard = useCopyToClipboard();

  const isRequiredOverrideEdits = !!workout.copyWorkoutId && isFirstCreate;
  const [hasDateChanged, setHasDateChanged] = useState(!isRequiredOverrideEdits);
  const setHasDateChangedTrue = useCallback(() => {
    setHasDateChanged(true);
  }, [setHasDateChanged]);
  const [hasParticipantsBeenConfirmed, setHasParticipantsBeenConfirmed] = useState(
    !(isRequiredOverrideEdits && hasAddParticipants),
  );
  const isErrorOverride = !hasDateChanged || !hasParticipantsBeenConfirmed;

  const isErrorName = !workout.workoutName;
  const isErrorParticipants = isEmptyArray(workout.participantIds);
  const isErrorType = isEmptyArray(workout.type);
  const isError =
    isErrorName || isErrorParticipants || isErrorType || isLinksError || isErrorOverride;
  const outlineStyle = isReadOnly ? {borderColor: theme.colors.readOnlyFieldBroder} : undefined;

  const startDate = useMemo(
    () => timestampToDate(workout.startedDateTime),
    [workout.startedDateTime],
  );
  const endDate = useMemo(() => timestampToDate(workout.endedDateTime), [workout.endedDateTime]);

  const today = useDateEveryMinute();
  const isTodayOrBefore = useIsWorkoutTodayOrBefore(workout);
  const isSwitchDisabled = !isTodayOrBefore || isReadOnly;

  const timeZone = useCurrentTimeZoneWithDefault();

  return (
    <>
      {DEV_FEATURE_FLAGS().isDebugViewEnabled && !isFirstCreate && (
        <CopyClipboardWrapper text={workout.id}>
          <Text py={1}>Work out ID: {workout.id}</Text>
        </CopyClipboardWrapper>
      )}
      {!isFirstCreate && (
        <Box flexDirection='row' justifyContent='flex-end'>
          <DuplicateWorkoutButton
            date={getIsoStringFromDate(startDate, timeZone)}
            workout={workout}
          />
          <Button
            icon='share-outline'
            ml={1}
            mode='outlined'
            mt={1}
            onPress={async () => {
              const shareUrl = createShareWorkoutLink(workout.id);
              await shareContent(shareUrl);
            }}
          >
            Share
          </Button>
        </Box>
      )}

      <Box my={1}>
        <TextInput
          autoFocus={isFirstCreate}
          error={hasWorkoutNameChanged && isErrorName}
          errorLabel='Workout name is required'
          label={`${CONTENT_CODES().WORKOUT.NAME_LABEL}*`}
          outlineStyle={outlineStyle}
          placeholder={CONTENT_CODES().WORKOUT.NAME_PLACEHOLDER}
          readOnly={isReadOnly}
          value={workout.workoutName}
          onChangeText={text => {
            setHasWorkoutNameChanged(true);
            onNameChange(text);
          }}
        />
      </Box>

      <Box mt={1}>
        <DateAndTimeComponent
          errorLabel={CONTENT_CODES().WORKOUT.OVERRIDE_DATE_ERROR_LABEL}
          isError={isErrorOverride && !hasDateChanged}
          isReadOnly={isReadOnly}
          label={CONTENT_CODES().WORKOUT.DATE_START_LABEL}
          value={startDate}
          onChange={onStartDateChange}
          onPress={setHasDateChangedTrue}
        />
      </Box>

      <Box flexDirection='row' mt={2}>
        <Box flex={1}>
          <DateAndTimeComponent
            isReadOnly={isReadOnly}
            label={CONTENT_CODES().WORKOUT.TIME_START_LABEL}
            type='time'
            value={startDate}
            onChange={onStartDateChange}
            onPress={setHasDateChangedTrue}
          />
        </Box>
        <Box flex={1} pl={1}>
          <DateAndTimeComponent
            isReadOnly={isReadOnly}
            label={CONTENT_CODES().WORKOUT.TIME_END_LABEL}
            type='time'
            value={endDate}
            onChange={onEndDateChange}
            onPress={setHasDateChangedTrue}
          />
        </Box>
      </Box>

      <Box flexDirection='row' pl={1} style={{paddingTop: 4}}>
        <Box flex={1}>
          <Text variant='bodySmall'>
            {CONTENT_CODES().WORKOUT.DURATION_LABEL}: {getWorkoutDurationDisplay(workout)}
          </Text>
        </Box>
        {!isTodayOrBefore && (
          <Box flex={1}>
            <Text variant='bodySmall'>
              {CONTENT_CODES().WORKOUT.DAYS_IN_FUTURE}:{' '}
              {getCalendarDaysDifference(startDate, today, timeZone)}
            </Text>
          </Box>
        )}
      </Box>

      <Box pb={2} position='relative' pt={1}>
        <TextInput
          multiline
          helperText={isReadOnly ? undefined : CONTENT_CODES().WORKOUT.NOTES_HELPER_TEXT}
          label={CONTENT_CODES().WORKOUT.NOTES_LABEL}
          outlineStyle={outlineStyle}
          placeholder={CONTENT_CODES().WORKOUT.NOTES_PLACEHOLDER}
          readOnly={isReadOnly}
          right={null}
          value={workout.notes}
          onChangeText={onNotesChange}
        />
        {workout.notes && (
          <IconButton
            icon='content-copy'
            style={{position: 'absolute', right: 0, top: 15, backgroundColor: '#fff'}}
            onPress={() => copyToClipboard(workout.notes, 'notes')}
          />
        )}
      </Box>

      <Box mb={1} mt={1}>
        <Text>
          {CONTENT_CODES().WORKOUT.TYPE_LABEL}*{'   '}
        </Text>
        <Grid container>
          {allWorkoutTypesToDisplay
            .filter(t => (isReadOnly ? workout.type.includes(t) : true))
            .map(type => (
              <Grid key={`WorkoutType-${type}`} item xs={isReadOnly ? 6 : 4}>
                <Checkbox.Item
                  label={getWorkoutTypeLabel(type, !isReadOnly)}
                  labelStyle={
                    isReadOnly
                      ? undefined
                      : {textAlign: 'center', paddingHorizontal: 0, marginHorizontal: 0}
                  }
                  mode='android'
                  status={workout.type.includes(type) ? 'checked' : 'unchecked'}
                  style={{paddingHorizontal: 0, marginHorizontal: 0}}
                  onPress={isReadOnly ? noop : () => onTypeChange(type)}
                  {...(isReadOnly ? {color: theme.colors.readOnlyIcon} : {})}
                  {...(isReadOnly ? {rippleColor: 'transparent'} : {})}
                />
              </Grid>
            ))}
        </Grid>
        {isErrorType && (
          <Text pl={2} style={{color: theme.colors.error}}>
            {CONTENT_CODES().WORKOUT.TYPE_INVALID_LABEL}
          </Text>
        )}
      </Box>

      {hasAddParticipants && (
        <>
          <Box pt={2} />
          <SearchAndAddUsers
            isReadOnly={isReadOnly || !isFirstCreate}
            label={`${CONTENT_CODES().WORKOUT.PARTICIPANTS_LABEL}* (trainer only)`}
            parentWorkout={isFirstCreate ? undefined : workout}
            searchQueryKey={searchQueryKey}
            userIds={workout.participantIds}
            onAdd={id => {
              onAddParticipant(id);
              setHasParticipantsBeenConfirmed(true);
            }}
            onRemove={id => {
              onRemoveParticipant(id);
              setHasParticipantsBeenConfirmed(true);
            }}
          />
          {!hasParticipantsBeenConfirmed && (
            <Box pt={1}>
              <Button
                icon='check-circle'
                mode='outlined'
                style={{borderColor: theme.colors.error}}
                textColor={theme.colors.error}
                onPress={() => setHasParticipantsBeenConfirmed(true)}
              >
                {CONTENT_CODES().WORKOUT.OVERRIDE_PARTICIPANT_ERROR_LABEL}
              </Button>
            </Box>
          )}
          <Box pt={2} />
        </>
      )}

      <Box py={1}>
        <WorkoutLinks
          isInError={isLinksError}
          label='Workout links'
          setIsInError={setIsLinksError}
          value={workout.links}
          valuePreview={workoutLink =>
            isValidLink(workoutLink.url) && (
              <TextLink
                style={{
                  verticalAlign: 'middle',
                }}
                onPress={() => {
                  void openURL(workoutLink.url);
                }}
              >
                {workoutLink.label || workoutLink.url}
              </TextLink>
            )}
          onChange={onLinksChanged}
        />
      </Box>

      <Box pb={4}>
        <PhotoListInput
          addLabel='Add photo'
          imageProps={{contentFit: 'contain', style: {height: 150}}}
          items={workout.images}
          label='Workout photos'
          noItemsLabel={<Text pl={1}>No photos added</Text>}
          storagePathPrefix={DOMAIN_CONSTANTS().CLOUD_STORAGE_PATH_PREFIXES.WORKOUTS(workout.id)}
          onChange={onImagesChanged}
        />
      </Box>

      <Switch
        disabled={isSwitchDisabled}
        label='Is workout complete?'
        value={workout.isCompleted}
        valueLabelFalse={
          <>
            <Box pl={1} />
            <WorkoutStatusIcon isComplete={workout.isCompleted} isTodayOrBefore={isTodayOrBefore} />
            <Text pl={1}>{isTodayOrBefore ? 'No 🚫' : 'Pending until start date ⏳'}</Text>
          </>
        }
        valueLabelTrue={
          <>
            <Box pl={1} />
            <WorkoutStatusIcon isComplete={workout.isCompleted} isTodayOrBefore={isTodayOrBefore} />
            <Text pl={1}>Yes ✅</Text>
          </>
        }
        onValueChange={onIsCompletedChange}
      />

      <Box mb={2} />

      {isReadOnly && (
        <Box my={2}>
          <Text variant='bodySmall'>
            {`Date and time workout created: ${formatTimestamp(workout.createdDateTime)}`}
          </Text>
        </Box>
      )}

      {submitText && isChanged && (
        <StickyAboveNavigation style={{bottom: 30}}>
          <Box mx='auto'>
            <Button
              disabled={isLoading || isError}
              icon={isFirstCreate ? 'check' : 'content-save-outline'}
              loading={isLoading}
              mode='contained'
              theme={{
                colors: {
                  surfaceDisabled: theme.colors.surfaceDisabledNoOpacity,
                  onSurfaceDisabled: theme.colors.onSurfaceDisabledNoOpacity,
                },
              }}
              onPress={onSubmit}
            >
              {submitText}
            </Button>
          </Box>
        </StickyAboveNavigation>
      )}

      <Box mb={8} />
    </>
  );
};
