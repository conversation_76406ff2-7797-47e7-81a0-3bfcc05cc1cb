import {useEffect, useState} from 'react';
import {Box, Button, Grid, IconButton, Text, TextInput} from '@base-components';
import type {InitializerOnChangeProps, WorkoutLink} from '@types';
import {
  getOrdinal,
  isEmptyArray,
  isNonEmptyArray,
  isString,
  isValidLink,
  useAppTheme,
  uuid,
} from '@utils';

type WorkoutLinksProps = InitializerOnChangeProps<WorkoutLink[] | undefined> & {
  isInError: boolean;
  label: React.ReactNode;
  setIsInError: (value: boolean) => void;
  value: WorkoutLink[] | undefined;
  valuePreview: (value: WorkoutLink) => React.ReactNode;
};

// eslint-disable-next-line max-lines-per-function -- TODO
export const WorkoutLinks: React.FC<WorkoutLinksProps> = ({
  isInError,
  label,
  onChange,
  setIsInError,
  value = [],
  valuePreview,
}) => {
  const theme = useAppTheme();
  const [editableLinks, setEditableLinks] = useState<string[]>([]);

  const handleDone = (id: string) => {
    setEditableLinks(p => p.filter(_ => _ !== id));
  };

  const handleEdit = (id: string) => {
    setEditableLinks(p => [...p, id]);
  };

  const handleAdd = () => {
    const id = uuid();
    onChange(prev => {
      handleEdit(id);

      return [...(prev ?? []), {url: '', label: '', id}];
    });
  };

  const handleRemove = (id: string) => {
    onChange(prev => {
      handleDone(id);

      return prev ? prev.filter(_ => _.id !== id) : prev;
    });
  };

  const handleChange = (id: string, newValue: Partial<WorkoutLink>) => {
    onChange(prev => prev?.map(item => (item.id === id ? {...item, ...newValue} : item)));
  };

  useEffect(() => {
    const isError = isNonEmptyArray(editableLinks) || !value.map(l => l.url).every(isValidLink);
    setIsInError(isError);
  }, [value, editableLinks, setIsInError]);

  return (
    <Box>
      <Box alignItems='center' flexDirection='row'>
        {label && isString(label) && <Text pr={1}>{label}</Text>}
        {(!label || !isString(label)) && label}
        <Button icon='link-variant-plus' mode='text' onPress={handleAdd}>
          Add link
        </Button>
      </Box>
      {isEmptyArray(value) && (
        <Box>
          <Text pl={1}>No links added</Text>
        </Box>
      )}
      {isNonEmptyArray(value) && (
        <Box flexShrink={1}>
          {value.map((item, index) => (
            <Grid key={`workout-link-${item.id}`} center container flexDirection='row' pb={0}>
              {editableLinks.includes(item.id) ? (
                <>
                  <Grid item pb={1} xs={7}>
                    <TextInput
                      dense
                      label={`${getOrdinal(index + 1)} link label`}
                      placeholder='Zoom workout link'
                      value={item.label}
                      onChangeText={v => handleChange(item.id, {label: v})}
                    />
                  </Grid>
                  <Grid item pt={1} xs={3}>
                    <Button
                      disabled={!isValidLink(item.url)}
                      icon='check-outline'
                      mode='text'
                      pl={1}
                      textColor='green'
                      onPress={() => handleDone(item.id)}
                    >
                      Done
                    </Button>
                  </Grid>
                  <Grid item pt={1} xs={2}>
                    <IconButton
                      icon='link-variant-remove'
                      iconColor={theme.colors.delete}
                      onPress={() => handleRemove(item.id)}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextInput
                      dense
                      error={!isValidLink(item.url)}
                      label={`${getOrdinal(index + 1)} link*`}
                      placeholder='https://zoom.us/j/1234567890'
                      value={item.url}
                      onChangeText={v => handleChange(item.id, {url: v})}
                    />
                  </Grid>
                  <Grid item pt={1} xs={12}>
                    {!isValidLink(item.url) && (
                      <Text style={{color: theme.colors.error}}>Link must be valid URL</Text>
                    )}
                  </Grid>
                </>
              ) : (
                <>
                  <Grid container item alignItems='center' flexDirection='row' xs={8}>
                    {valuePreview(item)}
                  </Grid>
                  <Grid item xs={2}>
                    <IconButton
                      icon='pencil-outline'
                      iconColor={theme.colors.edit}
                      onPress={() => handleEdit(item.id)}
                    />
                  </Grid>
                  <Grid item xs={2}>
                    <IconButton
                      icon='link-variant-remove'
                      iconColor={theme.colors.delete}
                      onPress={() => handleRemove(item.id)}
                    />
                  </Grid>
                </>
              )}
            </Grid>
          ))}
          {isInError && (
            <Grid item pt={1} xs={12}>
              <Text style={{color: theme.colors.error}}>
                All link fields must be saved to create the workout
              </Text>
            </Grid>
          )}
        </Box>
      )}
    </Box>
  );
};
