import {addMinutes} from 'date-fns';
import {useCallback, useState} from 'react';
import {Box, Button, DropDownSelfContained, Switch, Text, TextInput} from '@base-components';
import {CONTENT_CODES} from '@constants';
import {useEditChallengeValue, useEditLevel} from '@contexts';
import {
  anyTimestampToFirestoreClientTimestamp,
  type GroupLevelMetadata,
  type GroupsChallenge,
} from '@types';
import {emptyAggregationConfigBatched, emptyAggregationConfigRealTime, isDeepEqual} from '@utils';

const intervalsInMinutes = {
  '15 minutes': 15,
  '30 minutes': 30,
  '1 hour': 60,
  '2 hours': 60 * 2,
  '4 hours': 60 * 4,
  '8 hours': 60 * 8,
  '12 hours': 60 * 12,
  '1 day': 60 * 24,
} as const;

const ALL_INTERVALS_IN_MINUTES_OPTIONS = Object.entries(intervalsInMinutes).map(([key, value]) => ({
  label: key,
  value: value.toString(),
}));

type EditChallengeLevelProps = {
  level: number;
  onDismiss: () => void;
};

// eslint-disable-next-line max-lines-per-function -- form component
export const EditChallengeLevel: React.FC<EditChallengeLevelProps> = ({level, onDismiss}) => {
  const initialLevelMetadata = useEditChallengeValue<GroupsChallenge>().groupLevels.find(
    l => l.level === level,
  )!;
  const [levelMetadata, setLevelMetadata] = useState(initialLevelMetadata);

  const editLevel = useEditLevel();
  const hasChanged = !isDeepEqual(initialLevelMetadata, levelMetadata);
  const hasBatchTimeChanged =
    initialLevelMetadata.aggregationConfig.batchIntervalMinutes !==
    levelMetadata.aggregationConfig.batchIntervalMinutes;
  const onSubmit = useCallback(() => {
    if (!hasChanged) return;
    // Update next batch time if interval has changed
    const updatedLevelMetadata: GroupLevelMetadata =
      hasBatchTimeChanged && levelMetadata.aggregationConfig.batchIntervalMinutes
        ? {
            ...levelMetadata,
            aggregationConfig: {
              ...levelMetadata.aggregationConfig,
              nextAggregationDateTime: anyTimestampToFirestoreClientTimestamp(
                addMinutes(new Date(), levelMetadata.aggregationConfig.batchIntervalMinutes),
              ),
            },
          }
        : levelMetadata;
    editLevel(updatedLevelMetadata);
    onDismiss();
  }, [editLevel, hasBatchTimeChanged, hasChanged, levelMetadata, onDismiss]);

  return (
    <>
      <Text textAlign='center' variant='headlineMedium'>
        {CONTENT_CODES().CHALLENGE.EDIT.EDIT_LEVEL}
      </Text>

      <TextInput
        label={CONTENT_CODES().CHALLENGE.EDIT.LEVEL_LABEL}
        placeholder={CONTENT_CODES().CHALLENGE.EDIT.LEVEL_LABEL_PLACEHOLDER}
        value={levelMetadata.label}
        onChangeText={label => setLevelMetadata(p => ({...p, label}))}
      />

      <Box pb={2} />

      <TextInput
        label={CONTENT_CODES().CHALLENGE.EDIT.LEVEL_GROUP_LABEL}
        placeholder={CONTENT_CODES().CHALLENGE.EDIT.LEVEL_GROUP_LABEL_PLACEHOLDER}
        value={levelMetadata.groupLabel}
        onChangeText={groupLabel => setLevelMetadata(p => ({...p, groupLabel}))}
      />

      <Box pb={2} />

      <Switch
        label={CONTENT_CODES().CHALLENGE.EDIT.IS_FEED_ENABLED}
        value={levelMetadata.isFeedEnabled}
        valueLabelFalse='No'
        valueLabelTrue='Yes'
        onValueChange={() => setLevelMetadata(p => ({...p, isFeedEnabled: !p.isFeedEnabled}))}
      />

      <Box pb={2} />

      <Text pb={1} variant='titleSmall'>
        {CONTENT_CODES().CHALLENGE.EDIT.AGGREGATION_CONFIG_LABEL}
      </Text>

      <DropDownSelfContained
        label={CONTENT_CODES().CHALLENGE.EDIT.AGGREGATION_MODE}
        list={[
          {
            label: 'Batched',
            value: 'BATCHED',
          },
          {
            label: 'Real time',
            value: 'REAL_TIME',
          },
        ]}
        setValue={mode =>
          setLevelMetadata(p => {
            const aggregationConfig =
              mode === 'BATCHED'
                ? emptyAggregationConfigBatched()
                : emptyAggregationConfigRealTime();

            return {
              ...p,
              aggregationConfig,
            };
          })}
        value={levelMetadata.aggregationConfig.mode}
      />

      <Box pb={2} />

      {levelMetadata.aggregationConfig.mode === 'BATCHED' && (
        <>
          <DropDownSelfContained
            label={CONTENT_CODES().CHALLENGE.EDIT.AGGREGATION_INTERVAL}
            list={ALL_INTERVALS_IN_MINUTES_OPTIONS}
            setValue={intervalMinutes =>
              setLevelMetadata(p => {
                if (p.aggregationConfig.mode !== 'BATCHED') return p;

                return {
                  ...p,
                  aggregationConfig: {
                    ...p.aggregationConfig,
                    batchIntervalMinutes: Number(intervalMinutes),
                  },
                };
              })}
            value={levelMetadata.aggregationConfig.batchIntervalMinutes.toString()}
          />

          <Box pb={2} />
        </>
      )}

      <Box flexDirection='row' justifyContent='center' pt={2}>
        <Button disabled={!hasChanged} icon='check' mode='contained' onPress={onSubmit}>
          {CONTENT_CODES().CHALLENGE.EDIT.SAVE_LEVEL}
        </Button>
      </Box>
    </>
  );
};
