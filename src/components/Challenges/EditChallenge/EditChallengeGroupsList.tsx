import {useState} from 'react';
import {
  Box,
  Button,
  ContextMenuDotsHorizontal,
  IconButton,
  LoaderWrapper,
  LoadingIndicator,
  Text,
} from '@base-components';
import {
  useChallengeGroupCountParticipants,
  useChallengeGroupCreateMutation,
  useEditChallengeValue,
  useEditGroupActionSheet,
  useGroupsForChallengeLevel,
} from '@contexts';
import type {ChallengeGroupDocument, GroupsChallenge, UUIDString} from '@types';
import {isEmptyArray, isNonEmptyArray} from '@utils';
import {EditChallengeGroupParticipants} from './EditChallengeGroupParticipants';

type EditChallengeGroupCardProps = {
  group: ChallengeGroupDocument;
  groupIds: UUIDString[];
  indentLevel: number;
};

const EditChallengeGroupCard: React.FC<EditChallengeGroupCardProps> = ({
  group,
  groupIds,
  indentLevel,
}) => {
  const challenge = useEditChallengeValue<GroupsChallenge>();
  const isLeafLevel = group.level === challenge.groupLevels.length;
  const nextSubLevel = isLeafLevel ? group.level : group.level + 1;
  const nextGroupLevelMetadata = challenge.groupLevels.find(level => level.level === nextSubLevel);
  const {data: participantCount} = useChallengeGroupCountParticipants(group.challengeId, group.id);
  const {actions, isPending: isPendingDelete} = useEditGroupActionSheet(group, participantCount);
  const [isExpanded, setIsExpanded] = useState(false);
  const {createGroup, isPending: isPendingAddGroup} = useChallengeGroupCreateMutation(
    group.challengeId,
    nextGroupLevelMetadata,
    group,
  );
  const isPending = isPendingAddGroup;

  return (
    <>
      <Box
        style={{
          backgroundColor: '#fff',
          borderRadius: 10,
          paddingVertical: 8,
          paddingHorizontal: 16,
          marginLeft: 16 * indentLevel,
          marginBottom: 8,
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          flexWrap: 'wrap',
        }}
      >
        <Box flex={2}>
          <Text>
            {group.isPublicGroup ? '🅿 ' : ''}
            {group.name}
          </Text>
        </Box>

        <Box style={{flexDirection: 'row', alignItems: 'center'}}>
          {isPendingDelete ? <LoadingIndicator /> : <ContextMenuDotsHorizontal actions={actions} />}

          <IconButton
            icon='chevron-down'
            size={32}
            style={{padding: 0, margin: 0}}
            onPress={() => setIsExpanded(isEx => !isEx)}
          />
        </Box>
      </Box>

      {!isLeafLevel && nextGroupLevelMetadata && isExpanded && (
        <Box pb={2}>
          <EditChallengeGroupsList
            groupIds={groupIds}
            indentLevel={indentLevel + 1}
            level={group.level + 1}
            parentGroup={group}
          />
          <Button
            disabled={isPending}
            icon='plus'
            loading={isPending}
            ml={2 * (indentLevel + 1)}
            mode='outlined'
            mt={1}
            style={{maxWidth: '100%'}}
            onPress={createGroup}
          >
            Add {nextGroupLevelMetadata.groupLabel}
          </Button>
        </Box>
      )}

      {isLeafLevel && isExpanded && (
        <Box pb={2} pl={2 * (indentLevel + 1)}>
          <EditChallengeGroupParticipants group={group} groupIds={groupIds} />
        </Box>
      )}
    </>
  );
};

type EditChallengeGroupsListProps = {
  groupIds?: UUIDString[];
  indentLevel: number;
  level: number;
  parentGroup?: ChallengeGroupDocument;
};

export const EditChallengeGroupsList: React.FC<EditChallengeGroupsListProps> = ({
  groupIds,
  indentLevel,
  level,
  parentGroup,
}) => {
  const challenge = useEditChallengeValue();
  const {data: allGroups} = useGroupsForChallengeLevel(challenge.id, level, parentGroup?.id);

  return (
    <LoaderWrapper isLoading={!allGroups}>
      {isEmptyArray(allGroups) && indentLevel === 1 && <Text pl={2}>No groups at this level</Text>}
      {isNonEmptyArray(allGroups) &&
        allGroups.map(group => (
          <EditChallengeGroupCard
            key={`challenge-group-level-${level}-group-${group.id}`}
            group={group}
            groupIds={[...(groupIds ?? []), group.id]}
            indentLevel={indentLevel}
          />
        ))}
    </LoaderWrapper>
  );
};
