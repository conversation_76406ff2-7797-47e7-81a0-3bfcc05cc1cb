import {Box, ButtonCentered, Icon, SwitchWithLabel, TouchableHighlight} from '@base-components';
import {CONTENT_CODES} from '@constants';
import {
  useGetPushNotificationTypeIsEnabled,
  useNotificationSettingsIsShowFrequency,
  useNotificationTimeOfDay,
  useSetNotificationTimeOfDay,
  useTogglePushNotificationDisabled,
  useTogglePushNotificationType,
  useWellnessQuizSettingsState,
} from '@contexts';
import {ALL_WELLNESS_QUIZ_NOTIFICATION_TYPES, PushNotificationTypes} from '@types';
import {DateAndTimeComponent} from '../DateAndTimeComponent';
import {ManagedModal} from '../Shared';
import {NotificationSettingsHeader} from './NotificationSettingsHeader';

type WellnessQuizSettingsProps = Record<string, unknown>;

export const WellnessQuizSettings: React.FC<WellnessQuizSettingsProps> = () => {
  const {isWellnessQuizSettingsOpen, toggleValue} = useWellnessQuizSettingsState();
  const getType = useGetPushNotificationTypeIsEnabled();
  const isAllNotificationsEnabled = getType(ALL_WELLNESS_QUIZ_NOTIFICATION_TYPES);
  const toggle = useTogglePushNotificationType();
  const {toggle: toggleDisabled} = useTogglePushNotificationDisabled(
    ALL_WELLNESS_QUIZ_NOTIFICATION_TYPES,
  );
  const offsetDateTime = useNotificationTimeOfDay(PushNotificationTypes.WELLNESS_QUIZ_DAILY);
  const setOffset = useSetNotificationTimeOfDay(PushNotificationTypes.WELLNESS_QUIZ_DAILY);
  const {isShowFrequency, toggleValue: toggleIsShowFrequency} =
    useNotificationSettingsIsShowFrequency();

  return (
    <>
      <ManagedModal isFullWidth isOpen={isWellnessQuizSettingsOpen} onDismiss={toggleValue}>
        <NotificationSettingsHeader
          title={CONTENT_CODES().NOTIFICATIONS.SETTINGS.WELLNESS_EDUCATION.QUIZ.HEADER}
          onPressFrequency={toggleIsShowFrequency}
        />

        <SwitchWithLabel
          label={
            CONTENT_CODES().NOTIFICATIONS.SETTINGS.WELLNESS_EDUCATION.QUIZ
              .WELLNESS_QUIZ_QUESTION_LABEL
          }
          subLabel={
            isShowFrequency
              ? CONTENT_CODES().NOTIFICATIONS.SETTINGS.WELLNESS_EDUCATION.QUIZ
                .WELLNESS_QUIZ_QUESTION_FREQUENCY_LABEL
              : undefined
          }
          value={getType(PushNotificationTypes.WELLNESS_QUIZ_DAILY)}
          onValueChange={() => toggle(PushNotificationTypes.WELLNESS_QUIZ_DAILY)}
        />

        {getType(PushNotificationTypes.WELLNESS_QUIZ_DAILY) && (
          <DateAndTimeComponent
            label={CONTENT_CODES().NOTIFICATIONS.SETTINGS.WELLNESS_EDUCATION.QUIZ.TIME_LABEL}
            type='time'
            value={offsetDateTime}
            onChange={setOffset}
          />
        )}

        <SwitchWithLabel
          label={CONTENT_CODES().NOTIFICATIONS.SETTINGS.STREAKS.QUIZ_STREAK_START_LABEL}
          subLabel={
            isShowFrequency
              ? CONTENT_CODES().NOTIFICATIONS.SETTINGS.STREAKS.QUIZ_STREAK_START_FREQUENCY_LABEL
              : undefined
          }
          value={getType(PushNotificationTypes.WELLNESS_QUIZ_STREAK_CONTINUE)}
          onValueChange={() => toggle(PushNotificationTypes.WELLNESS_QUIZ_STREAK_CONTINUE)}
        />

        <SwitchWithLabel
          label={CONTENT_CODES().NOTIFICATIONS.SETTINGS.STREAKS.QUIZ_STREAK_REMINDER_LABEL}
          subLabel={
            isShowFrequency
              ? CONTENT_CODES().NOTIFICATIONS.SETTINGS.STREAKS.QUIZ_STREAK_REMINDER_FREQUENCY_LABEL
              : undefined
          }
          value={getType(PushNotificationTypes.WELLNESS_QUIZ_STREAK_REMINDER)}
          onValueChange={() => toggle(PushNotificationTypes.WELLNESS_QUIZ_STREAK_REMINDER)}
        />

        <ButtonCentered boxProps={{pt: 2}} mode='outlined' onPress={() => toggleValue()}>
          {CONTENT_CODES().NOTIFICATIONS.SETTINGS.CLOSE_MODAL}
        </ButtonCentered>
      </ManagedModal>

      <SwitchWithLabel
        label={CONTENT_CODES().NOTIFICATIONS.SETTINGS.WELLNESS_EDUCATION.QUIZ.LABEL}
        right={
          isAllNotificationsEnabled && (
            <TouchableHighlight onPress={toggleValue}>
              <Box flexDirection='row' pr={1}>
                <Icon name='cog-outline' size={24} />
              </Box>
            </TouchableHighlight>
          )
        }
        value={isAllNotificationsEnabled}
        onValueChange={toggleDisabled}
      />
    </>
  );
};
