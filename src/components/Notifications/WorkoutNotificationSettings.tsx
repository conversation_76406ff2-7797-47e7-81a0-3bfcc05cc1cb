import {Box, ButtonCentered, Icon, SwitchWithLabel, TouchableHighlight} from '@base-components';
import {CONTENT_CODES} from '@constants';
import {
  useGetPushNotificationTypeIsEnabled,
  useNotificationSettingsIsShowFrequency,
  useTogglePushNotificationDisabled,
  useTogglePushNotificationType,
  useWorkoutNotificationSettingsState,
} from '@contexts';
import {ALL_WORKOUT_CREATED_NOTIFICATION_TYPES, PushNotificationTypes} from '@types';
import {ManagedModal} from '../Shared';
import {NotificationSettingsHeader} from './NotificationSettingsHeader';

type WorkoutNotificationSettingsProps = Record<string, unknown>;

export const WorkoutNotificationSettings: React.FC<WorkoutNotificationSettingsProps> = () => {
  const {isWorkoutNotificationSettingsOpen, toggleValue} = useWorkoutNotificationSettingsState();
  const getType = useGetPushNotificationTypeIsEnabled();
  const isAllNotificationsEnabled = getType(ALL_WORKOUT_CREATED_NOTIFICATION_TYPES);
  const toggle = useTogglePushNotificationType();
  const {isShowFrequency, toggleValue: toggleIsShowFrequency} =
    useNotificationSettingsIsShowFrequency();

  const {toggle: toggleDisabled} = useTogglePushNotificationDisabled(
    ALL_WORKOUT_CREATED_NOTIFICATION_TYPES,
  );

  return (
    <>
      <ManagedModal isFullWidth isOpen={isWorkoutNotificationSettingsOpen} onDismiss={toggleValue}>
        <NotificationSettingsHeader
          title={CONTENT_CODES().NOTIFICATIONS.SETTINGS.WORKOUTS.HEADER}
          onPressFrequency={toggleIsShowFrequency}
        />

        <SwitchWithLabel
          label={CONTENT_CODES().NOTIFICATIONS.SETTINGS.WORKOUTS.SCHEDULED_LABEL}
          subLabel={
            isShowFrequency
              ? CONTENT_CODES().NOTIFICATIONS.SETTINGS.WORKOUTS.SCHEDULED_FREQUENCY_LABEL
              : undefined
          }
          value={getType(PushNotificationTypes.WORKOUT_CREATED_SCHEDULED)}
          onValueChange={() => toggle(PushNotificationTypes.WORKOUT_CREATED_SCHEDULED)}
        />
        <SwitchWithLabel
          label={CONTENT_CODES().NOTIFICATIONS.SETTINGS.WORKOUTS.COMPLETE_LABEL}
          subLabel={
            isShowFrequency
              ? CONTENT_CODES().NOTIFICATIONS.SETTINGS.WORKOUTS.COMPLETE_FREQUENCY_LABEL
              : undefined
          }
          value={getType([
            PushNotificationTypes.WORKOUT_CREATED_PAST_COMPLETED,
            PushNotificationTypes.WORKOUT_CREATED_PAST_NOT_COMPLETE,
          ])}
          onValueChange={() =>
            toggle([
              PushNotificationTypes.WORKOUT_CREATED_PAST_COMPLETED,
              PushNotificationTypes.WORKOUT_CREATED_PAST_NOT_COMPLETE,
            ])}
        />

        <ButtonCentered boxProps={{pt: 2}} mode='outlined' onPress={() => toggleValue()}>
          {CONTENT_CODES().NOTIFICATIONS.SETTINGS.CLOSE_MODAL}
        </ButtonCentered>
      </ManagedModal>
      <SwitchWithLabel
        label={CONTENT_CODES().NOTIFICATIONS.SETTINGS.WORKOUTS.LABEL}
        right={
          isAllNotificationsEnabled && (
            <TouchableHighlight onPress={toggleValue}>
              <Box flexDirection='row' pr={1}>
                <Icon name='cog-outline' size={24} />
              </Box>
            </TouchableHighlight>
          )
        }
        value={isAllNotificationsEnabled}
        onValueChange={toggleDisabled}
      />
    </>
  );
};
