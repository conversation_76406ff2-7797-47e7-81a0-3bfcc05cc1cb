import {Button} from '@base-components';
import {CONTENT_CODES} from '@constants';
import {useToggleState} from '@hooks';

export const RenderError: React.FC = () => {
  const [shouldThrowError, toggleShouldThrowError] = useToggleState(false);
  if (shouldThrowError) throw new Error('Custom render error thrown');

  return (
    <>
      <Button
        icon='alert-circle'
        mb={2}
        mode='outlined'
        onPress={() => {
          throw new Error('Custom async error thrown');
        }}
      >
        {CONTENT_CODES().SETTINGS.DEBUG_ACTIONS.ASYNC_ERROR}
      </Button>
      <Button icon='alert-octagon' mode='outlined' onPress={toggleShouldThrowError}>
        {CONTENT_CODES().SETTINGS.DEBUG_ACTIONS.RENDER_ERROR}
      </Button>
    </>
  );
};
