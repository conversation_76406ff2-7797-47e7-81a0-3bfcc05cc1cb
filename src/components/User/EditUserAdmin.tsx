import {useCallback, useMemo, useState} from 'react';
import {Accordion, Box, Button, RadioButton, Switch, Text, TextInput} from '@base-components';
import {CONTENT_CODES} from '@constants';
import {useAppUserByEmail, useAppUserSafe, useIsValidNewUserCredential} from '@contexts';
import {useAdminUpdateUserMutation} from '@data-hooks';
import type {AdminUpdateUserRequestDTO, UserType, UUIDString} from '@types';
import {allDisplayUserTypes, getAppUserTypeLabel} from '@types';
import {
  cleanEmail,
  cleanPhoneNumber,
  isDeepEqual,
  isDefinedString,
  isString,
  isValidPhoneTest,
  LOGGER,
} from '@utils';
import {SearchAndAddOrganizations} from '../Organizations';

type EditUserAdminProps = {
  allowInitialEmail?: true;
  initialAdminUpdateUserState: AdminUpdateUserRequestDTO;
  isFirstCreate?: true;
  isUseAuthUserOrgs?: true;
  onSubmitSuccess?: (user: AdminUpdateUserRequestDTO) => void;
  submitLabel: string;
  validUserTypes?: UserType[];
};

// eslint-disable-next-line max-lines-per-function, complexity -- form component
export const EditUserAdmin: React.FC<EditUserAdminProps> = ({
  allowInitialEmail,
  initialAdminUpdateUserState,
  isFirstCreate = false,
  isUseAuthUserOrgs,
  onSubmitSuccess,
  submitLabel,
  validUserTypes = allDisplayUserTypes,
}) => {
  const authUser = useAppUserSafe();
  const [user, setUser] = useState(initialAdminUpdateUserState);
  const {appUser: appUserFull} = useAppUserByEmail(user.email);
  const userOrganizationIds = useMemo(
    () =>
      isDefinedString(user.organizationIds)
        ? (user.organizationIds.split(',') as UUIDString[])
        : [],
    [user.organizationIds],
  );
  const {
    isPending: isLoadingAdminUpdateUser,
    mutateAsync: adminUpdateUser,
    reset,
  } = useAdminUpdateUserMutation();
  const [hasChangedEmail, setHasChangedEmail] = useState(!isFirstCreate);
  const {
    errorMessage: errorMessageEmail,
    isLoading: isLoaddingValidEmail,
    isValid: isValidEmail,
  } = useIsValidNewUserCredential(user.email, allowInitialEmail ? user.email : undefined);

  const isPhoneError = !!user.phoneNumber && !isValidPhoneTest(user.phoneNumber);

  const isChanged = !isDeepEqual(user, initialAdminUpdateUserState);
  const isLoadingAnything = isLoadingAdminUpdateUser || isLoaddingValidEmail;
  const isValidFirstName = user.firstName.length > 0;
  const isValidLastName = user.lastName.length > 0;
  const isValidInput =
    isValidEmail && isValidFirstName && isValidLastName && !isPhoneError && !isLoadingAnything;
  const isValidToSubmit =
    hasChangedEmail && !isLoadingAnything && isValidInput && (isFirstCreate || isChanged);

  const onReset = useCallback(() => {
    reset();
    setUser(initialAdminUpdateUserState);
  }, [initialAdminUpdateUserState, reset]);

  const onSubmit = useCallback(async () => {
    const response = await adminUpdateUser(user);
    if (!response || isString(response)) {
      LOGGER.error('Error updating user', response);

      return;
    }
    onSubmitSuccess?.(user);
  }, [adminUpdateUser, onSubmitSuccess, user]);

  const [hasChangedFirstName, setHasChangedFirstName] = useState(false);
  const [hasChangedLastName, setHasChangedLastName] = useState(false);

  return (
    <>
      {appUserFull && (
        <Box pb={2}>
          <Text>OS: {appUserFull.analytics?.deviceInfo?.osName}</Text>
          <Text>App version: {appUserFull.nativeAppVersion}</Text>
          {appUserFull.timeZone && <Text>Time zone: {appUserFull.timeZone}</Text>}
          <Text>Tracking device: {appUserFull.trackingDeviceType ?? 'not yet set'}</Text>
          {appUserFull.trackingApp && <Text>Tracking app: {appUserFull.trackingApp}</Text>}
        </Box>
      )}
      <Box mb={1}>
        <TextInput
          error={hasChangedFirstName && !isValidFirstName}
          errorLabel='First name is required'
          label={CONTENT_CODES().EDIT_USER.FIRST_NAME_LABEL}
          placeholder={CONTENT_CODES().EDIT_USER.FIRST_NAME_PLACEHOLDER}
          value={user.firstName}
          onChangeText={firstName => {
            setUser(p => ({...p, firstName}));
            setHasChangedFirstName(true);
          }}
        />
      </Box>

      <Box my={1}>
        <TextInput
          error={hasChangedLastName && !isValidLastName}
          errorLabel='Last name is required'
          label={CONTENT_CODES().EDIT_USER.LAST_NAME_LABEL}
          placeholder={CONTENT_CODES().EDIT_USER.LAST_NAME_PLACEHOLDER}
          value={user.lastName}
          onChangeText={lastName => {
            setUser(p => ({...p, lastName}));
            setHasChangedLastName(true);
          }}
        />
      </Box>

      <Box my={1}>
        <TextInput
          error={hasChangedEmail && isValidEmail === false}
          errorLabel={
            errorMessageEmail && errorMessageEmail !== 'undefined'
              ? `Error: ${errorMessageEmail}`
              : 'Error'
          }
          label={CONTENT_CODES().EDIT_USER.EMAIL_LABEL}
          placeholder={CONTENT_CODES().EDIT_USER.EMAIL_PLACEHOLDER}
          value={user.email}
          onChangeText={email => {
            setUser(p => ({...p, email: cleanEmail(email)}));
            setHasChangedEmail(true);
          }}
        />
      </Box>

      <Box my={1}>
        <TextInput
          error={isPhoneError}
          errorLabel='Must be a 10-digit phone number'
          keyboardType='phone-pad'
          label={CONTENT_CODES().EDIT_USER.PHONE_LABEL}
          placeholder={CONTENT_CODES().EDIT_USER.PHONE_PLACEHOLDER}
          value={user.phoneNumber ?? ''}
          onChangeText={value => {
            setUser(p => ({...p, phoneNumber: cleanPhoneNumber(value)}));
          }}
        />
      </Box>

      <Box my={1}>
        <Switch
          label='Is manual entry enabled?'
          value={!!user.isManualEntryEnabled}
          valueLabelFalse='No'
          valueLabelTrue='Yes'
          onValueChange={() => {
            setUser(p => ({
              ...p,
              isManualEntryEnabled: !p.isManualEntryEnabled,
            }));
          }}
        />
      </Box>

      <Box py={1} />

      <Box>
        <Text>User Type</Text>
        {validUserTypes.map(type => (
          <RadioButton
            key={`user-type-radio-${type}`}
            isFilled={user.type === type}
            label={getAppUserTypeLabel(type)}
            value={type}
            onPress={() => setUser(p => ({...p, type}))}
          />
        ))}
      </Box>

      <Box py={1} />

      <Accordion title={CONTENT_CODES().EDIT_USER.SEARCH_ORGANIZATIONS_LABEL}>
        <SearchAndAddOrganizations
          availableOrganizationIds={isUseAuthUserOrgs ? authUser.organizationIds : undefined}
          organizationIds={userOrganizationIds}
          searchQueryKey='organizationSearch'
          onAdd={id =>
            setUser(p =>
              p.organizationIds?.includes(id)
                ? p
                : {
                    ...p,
                    organizationIds: p.organizationIds ? `${p.organizationIds},${id}` : id,
                  },
            )}
          onRemove={id =>
            setUser(p => {
              if (!p.organizationIds) return p;
              if (!p.organizationIds.includes(id)) return p;
              const organizationIds = p.organizationIds
                .split(',')
                .filter(i => i !== id)
                .join(',');

              return {...p, organizationIds};
            })}
        />
      </Accordion>

      {!isFirstCreate && !isChanged && <Text pt={4}>You must change the user to save it</Text>}

      <Box pb={4} />

      <Box mx='auto'>
        <Button
          disabled={isLoadingAnything || !isValidToSubmit}
          icon='check'
          loading={isLoadingAnything}
          mode='contained'
          onPress={onSubmit}
        >
          {submitLabel}
        </Button>

        <Box my={1} />

        <Button
          disabled={isLoadingAnything}
          icon='arrow-u-left-top'
          mode='outlined'
          onPress={onReset}
        >
          {CONTENT_CODES().EDIT_USER.RESET_BUTTON}
        </Button>
      </Box>

      <Box pb={4} />
    </>
  );
};
