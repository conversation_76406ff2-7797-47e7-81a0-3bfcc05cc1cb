import {useMemo} from 'react';
import {Box, Text} from '@base-components';
import {CONTENT_CODES, DOMAIN_CONSTANTS} from '@constants';
import {useAppUserSafe, useIsStreakExplanationOpenState} from '@contexts';
import {useCurrentTimeZoneWithDefault, useIsoDateEveryDayChanged} from '@hooks';
import {getTimeZoneAbbreviation, type MovementStreakMetadataDay} from '@types';
import {formatMonthDayDate, getMovementStreakData, getStreakWeek} from '@utils';
import {InfoIconSmall} from '../Icons';
import {ManagedModal, ModalHeaderWithIcon} from '../Shared';
import {FirstStreakExplanationModal} from './FirstStreakExplanationModal';
import {StreakFlame} from './StreakFlame';
import {StreakWeekDays} from './StreakWeekDays';

const MovementStreakExplanationContent: React.FC = () => {
  const timeZone = useCurrentTimeZoneWithDefault();

  return (
    <>
      <Text variant='labelMedium'>What is a movement streak?</Text>
      <Text>
        A movement streak is a series of 2 or more consecutive days in which you have either
      </Text>
      <Text pl={1}>• logged a workout</Text>
      <Text pl={1}>• met your daily steps goal</Text>
      <Text pl={1}>• met your daily mileage goal</Text>
      <Text>on each day in the streak.</Text>

      <Box py={1} />

      <Text variant='labelMedium'>When does it reset?</Text>
      <Text>
        Your movement streak will reset to zero when you did not log a workout or meet a goal for a
        given day, according to your local time at midnight (i.e. 12:00am{' '}
        {getTimeZoneAbbreviation(timeZone)}).
      </Text>
    </>
  );
};

const MovementStreakMessage: React.FC<{streakData: Partial<MovementStreakMetadataDay>}> = ({
  streakData,
}) => (
  <Box flexDirection='row' pt={1}>
    {streakData.isInCurrentStreak && streakData.isCompletedToday && (
      <Text flex={1} flexWrap='wrap' variant='bodySmall'>
        {CONTENT_CODES().STREAK.STREAK_COMPLETE_TODAY(streakData.currentStreakInDays ?? 0)}
      </Text>
    )}

    {!streakData.isCompletedToday && (
      <Text flex={1} flexWrap='wrap' variant='bodySmall'>
        {CONTENT_CODES().STREAK.STREAK_NOT_YET_COMPLETE_TODAY(streakData.currentStreakInDays ?? 0)}
      </Text>
    )}
  </Box>
);

type MovementStreakContainerProps = Record<string, unknown>;

const useMovementStreakData = () => {
  const currentIsoDate = useIsoDateEveryDayChanged();
  const appUser = useAppUserSafe();
  const streakData = useMemo(
    () => getMovementStreakData(appUser, currentIsoDate, currentIsoDate),
    [appUser, currentIsoDate],
  );
  const weekDays = getStreakWeek(
    currentIsoDate,
    streakData,
    DOMAIN_CONSTANTS().STREAKS.MIN_MOVEMENT_STREAK_LENGTH,
  );

  return {weekDays, ...streakData};
};

export const MovementStreakContainer: React.FC<MovementStreakContainerProps> = () => {
  const streakData = useMovementStreakData();
  const {isStreakExplanationOpen, toggleValue} = useIsStreakExplanationOpenState();
  const timeZone = useCurrentTimeZoneWithDefault();
  const streakInDays = streakData.currentStreakInDays ?? 0;

  return (
    <Box style={{backgroundColor: '#fff', borderRadius: 10, padding: 16, flexDirection: 'row'}}>
      <FirstStreakExplanationModal hasStreak={streakData.isInCurrentStreak} />
      <Box flexGrow={1}>
        <StreakFlame value={streakData.currentStreakInDays ?? 0} />
        {streakInDays > 0 && streakData.streakStartIsoDate && (
          <Text textAlign='center' variant='bodySmall'>
            {formatMonthDayDate(streakData.streakStartIsoDate, timeZone)}
          </Text>
        )}
      </Box>

      <Box pl={2}>
        <Box alignItems='center' flexDirection='row'>
          <Text justifyContent='center' pr={1} variant='titleMedium'>
            {CONTENT_CODES().HOME.CALENDAR.MOVEMENT_STREAK_LABEL}
          </Text>
          <InfoIconSmall onPress={toggleValue} />
        </Box>

        <StreakWeekDays days={streakData.weekDays} />
        <MovementStreakMessage streakData={streakData} />
      </Box>

      <ManagedModal isFullWidth isOpen={isStreakExplanationOpen} onDismiss={toggleValue}>
        <ModalHeaderWithIcon onDismiss={toggleValue}>
          {CONTENT_CODES().STREAK.EXPLANATION_MODAL.TITLE}
        </ModalHeaderWithIcon>

        <MovementStreakExplanationContent />

        <Box py={1} />
      </ManagedModal>
    </Box>
  );
};
