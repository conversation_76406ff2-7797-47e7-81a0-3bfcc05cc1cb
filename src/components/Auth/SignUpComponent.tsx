import {useState} from 'react';
import {openInbox} from 'react-native-email-link';
import {Box, Button, Text, TextInput, TextLink, TouchableHighlight} from '@base-components';
import {CONTENT_CODES} from '@constants';
import {useIsValidNewUserCredential, useSignUpState} from '@contexts';
import {useLinkTo} from '@navigation';
import {useAppTheme} from '@utils';

type SignUpComponentProps = Record<string, unknown>;

// eslint-disable-next-line max-lines-per-function -- form component
export const SignUpComponent: React.FC<SignUpComponentProps> = () => {
  const to = useLinkTo();
  const theme = useAppTheme();
  const {
    isLoggingIn,
    loginError,
    onChangeEmail,
    onChangeFirstName,
    onChangeLastName,
    onChangePhoneNumber,
    onSubmit,
    signUpState,
    signUpStatus,
  } = useSignUpState();
  const [hasChangedEmail, setHasEmailChanged] = useState(false);
  const [hasChangedFirstName, setHasFirstNameChanged] = useState(false);
  const [hasChangedLastName, setHasLastNameChanged] = useState(false);

  const {
    errorMessage: errorMessageEmail,
    isLoading: isLoaddingValidEmail,
    isValid: isValidEmail,
  } = useIsValidNewUserCredential(signUpState.email, undefined);

  const {
    errorMessage: errorMessagePhone,
    isLoading: isLoaddingValidPhone,
    isValid: isValidPhone,
  } = useIsValidNewUserCredential(signUpState.phoneNumber, undefined, true);

  const isValid =
    !!isValidEmail && !!isValidPhone && !!signUpState.firstName && !!signUpState.lastName;
  const isLoadingSubmit = signUpStatus === 'pending';
  const isLoadingAnything =
    isLoaddingValidEmail || isLoaddingValidPhone || isLoadingSubmit || isLoggingIn;

  return (
    <Box minWidth='100%'>
      <TextInput
        autoComplete='given-name'
        error={hasChangedFirstName && !signUpState.firstName}
        errorLabel='First name is required'
        label={`${CONTENT_CODES().AUTH.FIRST_NAME_LABEL}*`}
        placeholder={CONTENT_CODES().AUTH.FIRST_NAME_PLACEHOLDER}
        value={signUpState.firstName}
        onBlur={() => setHasFirstNameChanged(true)}
        onChangeText={text => {
          onChangeFirstName(text);
          setHasFirstNameChanged(true);
        }}
      />

      <Box pt={2} />

      <TextInput
        autoComplete='family-name'
        error={hasChangedLastName && !signUpState.lastName}
        errorLabel='Last name is required'
        label={`${CONTENT_CODES().AUTH.LAST_NAME_LABEL}*`}
        placeholder={CONTENT_CODES().AUTH.LAST_NAME_PLACEHOLDER}
        value={signUpState.lastName}
        onBlur={() => setHasLastNameChanged(true)}
        onChangeText={text => {
          onChangeLastName(text);
          setHasLastNameChanged(true);
        }}
      />

      <Box pt={2} />

      <TextInput
        autoComplete='email'
        error={hasChangedEmail && isValidEmail === false}
        errorLabel={
          errorMessageEmail && errorMessageEmail !== 'undefined'
            ? `Error: ${errorMessageEmail}`
            : 'Error'
        }
        label={`${CONTENT_CODES().AUTH.CREDENTIAL_EMAIL_LABEL}*`}
        placeholder={CONTENT_CODES().AUTH.EMAIL_PLACEHOLDER}
        value={signUpState.email}
        onBlur={() => setHasEmailChanged(true)}
        onChangeText={text => {
          onChangeEmail(text);
          setHasEmailChanged(true);
        }}
      />

      <Box pt={2} />

      <TextInput
        autoComplete='tel'
        error={isValidPhone === false}
        errorLabel={
          errorMessagePhone && errorMessagePhone !== 'undefined'
            ? `Error: ${errorMessagePhone}`
            : 'Error'
        }
        keyboardType='phone-pad'
        label={CONTENT_CODES().AUTH.CREDENTIAL_PHONE_LABEL}
        placeholder={CONTENT_CODES().AUTH.PHONE_NUMBER_PLACEHOLDER}
        value={signUpState.phoneNumber ?? ''}
        onChangeText={onChangePhoneNumber}
      />

      <Box pt={1} />

      <Button
        disabled={isLoadingAnything || !isValid || signUpStatus !== 'not started'}
        icon='account'
        loading={isLoadingAnything}
        mb={2}
        mode='contained'
        mt={1}
        onPress={onSubmit}
      >
        {CONTENT_CODES().AUTH.SIGN_UP_BUTTON}
      </Button>

      {signUpStatus === 'success' && (
        <Box pb={2}>
          <Text pb={1}>{CONTENT_CODES().AUTH.SIGN_UP_SUCCESS}</Text>

          <Text pb={1}>{CONTENT_CODES().AUTH.SIGN_UP_MAGIC_LINK}</Text>

          <Button icon='email-open-outline' mode='contained' onPress={() => openInbox()}>
            {CONTENT_CODES().AUTH.EMAIL_OPEN_BUTTON}
          </Button>
        </Box>
      )}

      {signUpStatus === 'error' && (
        <Box pb={2}>
          <Text pb={1}>{CONTENT_CODES().AUTH.SIGN_UP_ERROR}</Text>
        </Box>
      )}

      {loginError && (
        <Box pb={1}>
          <Text style={{color: theme.colors.error}} variant='bodyMedium'>
            Error: {loginError}
          </Text>
        </Box>
      )}

      <Box pt={1} />

      <TouchableHighlight onPress={() => to.loginScreen()}>
        <Text py={1}>
          {CONTENT_CODES().AUTH.ALREADY_HAVE_ACCOUNT}{' '}
          <TextLink disableIcon onPress={() => to.loginScreen()}>
            {CONTENT_CODES().AUTH.CLICK_TO_LOGIN}
          </TextLink>
        </Text>
      </TouchableHighlight>
    </Box>
  );
};
