import {isDev} from './platformProjectEnvironmentConstants';

const isDevMode = isDev || (false as boolean);
const isShowAllDebug = false as boolean;
// eslint-disable-next-line @typescript-eslint/naming-convention, complexity -- constants
export const DEV_FEATURE_FLAGS = () =>
  ({
    isAppStoreRatingEnabled: false as boolean,
    isMockUserEnabled: isDevMode && (false as boolean),
    isAnalyticsEnabled: !isDevMode,
    isDebugViewEnabled: isDevMode && (false as boolean),
    isLogSearchResultsEnabled: isDevMode && (false as boolean),
    isDebugEnv: (isDevMode && (false as boolean)) || isShowAllDebug,
    isDebugMMKVEnabled: (isDevMode && (false as boolean)) || isShowAllDebug,
    isDebugAppUserChanged: (isDevMode && (false as boolean)) || isShowAllDebug,
    isDebugAppStateBackground: (isDevMode && (false as boolean)) || isShowAllDebug,
    isDebugHealthSync: (isDevMode && (false as boolean)) || isShowAllDebug,
    isDebugFitbit: (isDevMode && (false as boolean)) || isShowAllDebug,
    isDebugTrackingDevice: (isDevMode && (false as boolean)) || isShowAllDebug,
    isDebugCache: (isDevMode && (false as boolean)) || isShowAllDebug,
    isDebugExpoUpdate: (isDevMode && (false as boolean)) || isShowAllDebug,
    isDebugCalendar: (isDevMode && (false as boolean)) || isShowAllDebug,
    isDebugNotification: (isDevMode && (false as boolean)) || isShowAllDebug,
    isDebugNavigation: (isDevMode && (false as boolean)) || isShowAllDebug,
    isDebugAuth: (isDevMode && (false as boolean)) || isShowAllDebug,
    isDebugStrictMode: (isDevMode && (false as boolean)) || isShowAllDebug,
    isDebugImage: (isDevMode && (false as boolean)) || isShowAllDebug,
    isDebugChallenge: (isDevMode && (false as boolean)) || isShowAllDebug,
    isDebugMovementStreak: (isDevMode && (false as boolean)) || isShowAllDebug,
    isDebugQueries: (isDevMode && (false as boolean)) || isShowAllDebug,
    isPerformanceDebuggingNetworkEnabled: isDevMode && (false as boolean),
    isPerformanceDebuggingTimingEnabled: isDevMode && (false as boolean),
    isPerformanceDebuggingDedupeEnabled: isDevMode && (false as boolean),
    isPerformanceDebuggingRendersEnabled: isDevMode && (false as boolean),
    isAppStoreScreenshotMode: false as boolean,
  }) as const;
