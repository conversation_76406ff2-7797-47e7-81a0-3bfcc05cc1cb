import {useEffect} from 'react';
import {
  Box,
  Button,
  Chip,
  IconButton,
  LoadingIndicator,
  StickyAboveNavigation,
  Text,
} from '@base-components';
import {
  ChallengeAcceptReject,
  ChallengeCountdown,
  ChallengeHeaderPhoto,
  ChallengeInitializeStage,
  ChallengeLeaderboard,
  ChallengeProgress,
  CopyClipboardWrapper,
  EditGoalModalChallenge,
  ScreenContent,
  ScreenHeader,
} from '@components';
import {CONTENT_CODES, DEV_FEATURE_FLAGS} from '@constants';
import {
  ScreenWrapper,
  useAppUserSafe,
  useChallengeById,
  useIsCoach,
  useViewChallengeActionSheet,
} from '@contexts';
import {useLinkTo, type ViewChallengeScreenProps} from '@navigation';
import {type Challenge, isParticipantsChallenge} from '@types';
import {memoComponent} from '@utils';

const ViewChallengeContent: React.FC<{challenge: Challenge}> = memoComponent(({challenge}) => (
  <>
    <ChallengeInitializeStage challenge={challenge} />

    {challenge.flags?.isDraft && (
      <StickyAboveNavigation>
        <Box flexDirection='row' justifyContent='flex-end'>
          <Chip icon='file-document-edit-outline' mode='outlined' textStyle={{color: 'red'}}>
            DRAFT STATE
          </Chip>
        </Box>
      </StickyAboveNavigation>
    )}

    <EditGoalModalChallenge />

    {DEV_FEATURE_FLAGS().isDebugViewEnabled && (
      <CopyClipboardWrapper text={challenge.id}>
        <Text>{challenge.id}</Text>
      </CopyClipboardWrapper>
    )}

    <ChallengeHeaderPhoto challenge={challenge} />

    <ChallengeAcceptReject hasHeading hasPb challenge={challenge} />

    <ChallengeProgress
      challenge={challenge}
      header={CONTENT_CODES().CHALLENGE.VIEW.PROGRESS_HEADER}
    />

    <ChallengeCountdown challenge={challenge} />

    <ChallengeLeaderboard challenge={challenge} />
  </>
));

export const ViewChallengeScreen: React.FC<ViewChallengeScreenProps> = ({route}) => {
  const to = useLinkTo();
  const {challengeId} = route.params;
  const challenge = useChallengeById(challengeId);
  const appUser = useAppUserSafe();
  const isCoach = useIsCoach();
  const isEditor = challenge?.editorIds.includes(appUser.id);
  const headerTitle = challenge?.challengeName ?? CONTENT_CODES().CHALLENGE.VIEW.DEFAULT_HEADER;
  const {isPending, mutateAsync: onPressDetails} = useViewChallengeActionSheet(
    challengeId,
    !isEditor,
  );

  useEffect(() => {
    // Skip going back if coach or editor
    if (isCoach || isEditor) return;
    // Make the user go back if
    //   1. Challenge has loaded
    //   2. Challenge is a team challenge
    //   3. User is NOT participant
    if (
      challenge &&
      isParticipantsChallenge(challenge) &&
      !challenge.participantIds.includes(appUser.id)
    ) {
      to.goBack();
    }
  }, [appUser.id, challenge, isCoach, isEditor, to]);

  return (
    <ScreenWrapper>
      <ScreenHeader
        right={
          isPending ? (
            <Box alignItems='center' height={52} justifyContent='center' width={52}>
              <LoadingIndicator />
            </Box>
          ) : (
            <IconButton icon='dots-vertical' onPress={() => onPressDetails()} />
          )
        }
        title={headerTitle}
      />

      <ScreenContent disableXPadding>
        {!challenge && <LoadingIndicator />}
        {challenge && (
          <>
            {isEditor && (
              <Button
                icon='account-supervisor-circle-outline'
                mb={2}
                mode='outlined'
                mx={2}
                onPress={() => to.challengeAdminScreen({challengeId})}
              >
                Go to admin screen
              </Button>
            )}
            <ViewChallengeContent challenge={challenge} />
          </>
        )}
      </ScreenContent>

      <Box py={2} />
    </ScreenWrapper>
  );
};
