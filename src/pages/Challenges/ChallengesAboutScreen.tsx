import {Images} from '@assets';
import {Accordion, Box, Divider, Icon, Surface2, Text, TouchableHighlight} from '@base-components';
import {ScreenContent, ScreenHeader} from '@components';
import {CONTENT_CODES} from '@constants';
import {ScreenWrapper} from '@contexts';
import type {ChallengesAboutScreenProps} from '@navigation';
import {useAppTheme} from '@utils';

type FeatureCardProps = {
  description: string;
  iconName: string;
  onPress?: (() => void) | undefined;
  title: string;
};

const FeatureCard: React.FC<FeatureCardProps> = ({description, iconName, onPress, title}) => {
  const theme = useAppTheme();

  return (
    <TouchableHighlight disabled={!onPress} onPress={() => onPress?.()}>
      <Surface2>
        <Box alignItems='center' flexDirection='row' mb={1}>
          <Box
            mr={2}
            style={{
              backgroundColor: theme.colors.primaryContainer,
              borderRadius: 25,
              width: 50,
              height: 50,
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Icon color={theme.colors.primary} name={iconName} size={28} />
          </Box>
          <Text variant='titleMedium'>{title}</Text>
        </Box>
        <Text variant='bodyMedium'>{description}</Text>
      </Surface2>
    </TouchableHighlight>
  );
};

const ContentSection: React.FC<{
  content: string;
  header: string;
  iconName: string;
  isAccordion?: boolean;
}> = ({content, header, iconName, isAccordion = false}) => {
  const theme = useAppTheme();

  if (isAccordion) {
    return (
      <Box pb={2}>
        <Accordion title={header} titleIcon={iconName}>
          <Box pt={1}>
            <Text textAlign='left' variant='bodyMedium'>
              {content}
            </Text>
          </Box>
        </Accordion>
      </Box>
    );
  }

  return (
    <Box pb={2}>
      <Box alignItems='center' flexDirection='row'>
        <Icon color={theme.colors.primary} name={iconName} size={24} />
        <Text ml={1} pb={1} pt={2} variant='titleSmall'>
          {header}
        </Text>
      </Box>
      <Text pb={1} textAlign='left' variant='bodyMedium'>
        {content}
      </Text>
    </Box>
  );
};

// Banner component to reduce main component size
const ChallengeBanner: React.FC = () => (
  <Box mb={2} style={{borderRadius: 10, overflow: 'hidden'}}>
    <Images.mileageChallengeBanner style={{width: '100%', height: 150, borderRadius: 10}} />
    <Box
      bottom={0}
      left={0}
      p={2}
      position='absolute'
      right={0}
      style={{
        backgroundColor: 'rgba(0,0,0,0.6)',
      }}
    >
      <Text style={{color: 'white'}} variant='titleLarge'>
        FlyFit Challenges
      </Text>
    </Box>
  </Box>
);

// Feature cards section to reduce main component size
const FeatureCardsSection: React.FC = () => (
  <>
    <Text mb={2} variant='titleMedium'>
      Key Features
    </Text>
    <Box mb={2}>
      <FeatureCard
        description={CONTENT_CODES().CHALLENGE.ABOUT.FEATURE_TEAMS_DESCRIPTION}
        iconName='account-group'
        title={CONTENT_CODES().CHALLENGE.ABOUT.FEATURE_TEAMS_TITLE}
      />
    </Box>

    <Box mb={2}>
      <FeatureCard
        description={CONTENT_CODES().CHALLENGE.ABOUT.FEATURE_STAGES_DESCRIPTION}
        iconName='progress-check'
        title={CONTENT_CODES().CHALLENGE.ABOUT.FEATURE_STAGES_TITLE}
      />
    </Box>

    <Box mb={2}>
      <FeatureCard
        description={CONTENT_CODES().CHALLENGE.ABOUT.FEATURE_LEADERBOARDS_DESCRIPTION}
        iconName='chart-line'
        title={CONTENT_CODES().CHALLENGE.ABOUT.FEATURE_LEADERBOARDS_TITLE}
      />
    </Box>

    <Box mb={2}>
      <FeatureCard
        description={CONTENT_CODES().CHALLENGE.ABOUT.FEATURE_FEEDS_DESCRIPTION}
        iconName='forum'
        title={CONTENT_CODES().CHALLENGE.ABOUT.FEATURE_FEEDS_TITLE}
      />
    </Box>

    <Box mb={2}>
      <FeatureCard
        description={CONTENT_CODES().CHALLENGE.ABOUT.FEATURE_TRACKING_DESCRIPTION}
        iconName='watch'
        title={CONTENT_CODES().CHALLENGE.ABOUT.FEATURE_TRACKING_TITLE}
      />
    </Box>
  </>
);

// Accordion sections component to reduce main component size
const AccordionSections: React.FC = () => (
  <>
    <Text mb={2} variant='titleMedium'>
      Learn More
    </Text>

    <ContentSection
      isAccordion
      content={CONTENT_CODES().CHALLENGE.ABOUT.BENEFITS_BODY}
      header={CONTENT_CODES().CHALLENGE.ABOUT.BENEFITS_HEADER}
      iconName='star-outline'
    />

    <ContentSection
      isAccordion
      content={CONTENT_CODES().CHALLENGE.ABOUT.HOW_IT_WORKS_BODY}
      header={CONTENT_CODES().CHALLENGE.ABOUT.HOW_IT_WORKS_HEADER}
      iconName='information-outline'
    />

    <ContentSection
      isAccordion
      content={CONTENT_CODES().CHALLENGE.ABOUT.TRACK_PROGRESS_BODY}
      header={CONTENT_CODES().CHALLENGE.ABOUT.TRACK_PROGRESS_HEADER}
      iconName='run'
    />

    <ContentSection
      isAccordion
      content={CONTENT_CODES().CHALLENGE.ABOUT.WELLNESS_CONTENT_BODY}
      header={CONTENT_CODES().CHALLENGE.ABOUT.WELLNESS_CONTENT_HEADER}
      iconName='book-open-variant'
    />

    <ContentSection
      isAccordion
      content={CONTENT_CODES().CHALLENGE.ABOUT.GET_MOVING_BODY}
      header={CONTENT_CODES().CHALLENGE.ABOUT.GET_MOVING_HEADER}
      iconName='arm-flex'
    />
  </>
);

export const ChallengesAboutScreen: React.FC<ChallengesAboutScreenProps> = () => {
  const theme = useAppTheme();

  return (
    <ScreenWrapper>
      <ScreenHeader title={CONTENT_CODES().CHALLENGE.ABOUT.HEADER_TITLE} />

      <ScreenContent>
        {/* Banner Image */}
        <ChallengeBanner />

        {/* What is a Challenge - Featured Section */}
        <ContentSection
          content={CONTENT_CODES().CHALLENGE.ABOUT.WHAT_IS_A_CHALLENGE_BODY}
          header={CONTENT_CODES().CHALLENGE.ABOUT.WHAT_IS_A_CHALLENGE_HEADER}
          iconName='trophy-outline'
        />

        <Divider mb={3} />

        {/* Key Features */}
        <FeatureCardsSection />

        <Divider mb={3} />

        {/* Detailed Sections as Accordions */}
        <AccordionSections />

        <Box
          mt={3}
          mx={-2}
          py={6}
          style={{
            backgroundColor: theme.colors.labelGray,
          }}
        >
          <Text style={{color: 'white'}} textAlign='center' variant='titleSmall'>
            Ready to get started?
          </Text>
          <Text mt={1} px={2} style={{color: 'white'}} textAlign='center' variant='bodyMedium'>
            Join a challenge today and start your fitness journey with FlyFit!
          </Text>
        </Box>
      </ScreenContent>
    </ScreenWrapper>
  );
};
