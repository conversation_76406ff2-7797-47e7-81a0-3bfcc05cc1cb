import {Box, Button, LoadingIndicator, Text} from '@base-components';
import {OrganizationsList, ScreenContent, ScreenHeader, SearchUsers} from '@components';
import {CONTENT_CODES} from '@constants';
import {ScreenWrapper, useOrganizationCollection} from '@contexts';
import {type AdminHomeScreenProps, useLinkTo} from '@navigation';

export const AdminHomeScreen: React.FC<AdminHomeScreenProps> = () => {
  const {data: organizations, isLoading} = useOrganizationCollection();
  const to = useLinkTo();

  return (
    <ScreenWrapper>
      <ScreenHeader left={<Box />} right={<Box />} title={CONTENT_CODES().ADMIN.HEADER_TITLE} />

      <ScreenContent>
        <Box alignItems='center' flexDirection='row' justifyContent='space-between' width='100%'>
          <Text variant='headlineMedium'>{CONTENT_CODES().ADMIN.ORGANIZATIONS_LABEL}</Text>
          <Button
            icon='plus'
            mode='outlined'
            onPress={() =>
              to.createOrganizationScreen({
                adminUsersSearch: '',
                coachUsersSearch: '',
                parentOrganizationSearch: '',
                clientUsersSearch: '',
              })}
          >
            {CONTENT_CODES().ADMIN.CREATE_ORGANIZATION_LABEL}
          </Button>
        </Box>

        <Box pt={1}>
          {isLoading && <LoadingIndicator />}
          {!isLoading && organizations && (
            <OrganizationsList isViewOrgEnabled organizationIds={organizations.map(o => o.id)} />
          )}
        </Box>

        <Box pb={3} />

        <Box alignItems='center' flexDirection='row' justifyContent='space-between' width='100%'>
          <Text variant='headlineMedium'>{CONTENT_CODES().ADMIN.USERS_LABEL}</Text>
          <Button
            icon='plus'
            mode='outlined'
            onPress={() => to.createNewUser({organizationSearch: ''})}
          >
            {CONTENT_CODES().ADMIN.CREATE_USER_LABEL}
          </Button>
        </Box>

        <Box pt={1}>
          <SearchUsers
            searchQueryKey='usersSearchTerm'
            onPressUser={userId => to.editUserScreen({userId, organizationSearch: ''})}
          />
        </Box>

        <Box py={2} />
      </ScreenContent>
    </ScreenWrapper>
  );
};
