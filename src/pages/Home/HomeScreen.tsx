import {HealthScore, Images} from '@assets';
import {
  Box,
  Button,
  LoadingIndicator,
  Text,
  ToggleButton,
  TouchableHighlight,
} from '@base-components';
import {
  ChallengeMilestones,
  HealthSyncFirstLoadContainer,
  HealthSyncStepModal,
  HomeCalendarWrapper,
  InviteCodeModal,
  MovementStreakContainer,
  NotificationPopUpModal,
  PushNotificationToggleBell,
  ScreenContent,
  TrackingDeviceModal,
  UpdateBanner,
  WeightContainer,
  WellnessBlogContainer,
  WellnessQuizContainer,
} from '@components';
import {CONTENT_CODES} from '@constants';
import {
  HasAnyHealthPermissionsWrapper,
  ScreenWrapper,
  useAppUserMetadataHealthData,
  useAppUserSafe,
  useSyncRefreshAll,
} from '@contexts';
import {useLinkTo} from '@navigation';
import {ALL_WELLNESS_QUIZ_NOTIFICATION_TYPES} from '@types';
import {formatDisplayMiles, memoComponent, useAppTheme} from '@utils';

const HomeHeaderNonVisibleContainer: React.FC = () => (
  <>
    <NotificationPopUpModal />
    <HealthSyncFirstLoadContainer />
    <TrackingDeviceModal />
    <InviteCodeModal />
  </>
);

const HomeContent: React.FC = () => (
  <>
    {/* {isAndroid && (
      <HasAnyHealthPermissionsWrapper>
        <HealthConnectDataSourceSettings hideUi />
      </HasAnyHealthPermissionsWrapper>
    )} */}

    <ChallengeMilestones />

    <HealthSyncStepModal hasPaddingTop />

    <Box py={1}>
      <MovementStreakContainer />
    </Box>

    <Box mx={-2}>
      <HomeCalendarWrapper />
    </Box>

    <Box alignItems='center' flexDirection='row' justifyContent='space-between' mt={2} py={1}>
      <Text variant='headlineMedium'>Quiz</Text>

      <PushNotificationToggleBell
        settingKey='isWellnessBlogSettingsOpen'
        types={ALL_WELLNESS_QUIZ_NOTIFICATION_TYPES}
      />
    </Box>

    <WellnessQuizContainer />

    <Box pb={2} />

    <WellnessBlogContainer />

    <Text mt={2} py={1} variant='headlineMedium'>
      Weight
    </Text>

    <WeightContainer />

    {/* <Text mt={2} py={1} variant='headlineMedium'>
      Summary Numbers
    </Text>

    <SummaryNumbersIndividual /> */}

    <Box mt={4} />
  </>
);

const HomeContentMemo = memoComponent(HomeContent);
const HomeHeaderNonVisibleContainerMemo = memoComponent(HomeHeaderNonVisibleContainer);

export const HomeScreen: React.FC = () => {
  const to = useLinkTo();
  const theme = useAppTheme();
  const {isPending, onHapticRefresh} = useSyncRefreshAll();
  const appUser = useAppUserSafe();
  const {totalDistanceMeters = 0} = useAppUserMetadataHealthData(appUser.id) ?? {};

  return (
    <ScreenWrapper topElement={<UpdateBanner />} onRefresh={onHapticRefresh}>
      <HomeHeaderNonVisibleContainerMemo />

      <Box flexDirection='row'>
        <TouchableHighlight
          style={{
            flex: 1,
            alignItems: 'center',
            flexDirection: 'row',
            justifyContent: 'flex-start',
            paddingLeft: 16,
          }}
          onPress={() => to.profile({userId: appUser.id})}
        >
          <>
            <Images.defaultProfile
              overrideSource={appUser.profilePicture}
              style={{width: 82, height: 82, borderRadius: 41}}
            />
            <Box pl={2}>
              <Text variant='headlineMedium'>{appUser.firstName}</Text>
              <Box alignItems='center' flexDirection='row'>
                <HealthScore fill={theme.colors.healthScoreColor} />
                <Text pl={1}>{formatDisplayMiles(totalDistanceMeters, 0)} mi</Text>
              </Box>
            </Box>
          </>
        </TouchableHighlight>

        <Box
          alignItems='flex-start'
          flexDirection='row'
          flexShrink={1}
          justifyContent='flex-end'
          pr={1}
        >
          <HasAnyHealthPermissionsWrapper>
            <Box flexDirection='row'>
              {isPending && <LoadingIndicator />}
              <Button
                compact
                buttonColor={theme.colors.labelDarkBlue}
                icon='sync'
                ml={1}
                mode='contained'
                onLongPress={() => to.healthSyncSettings()}
                onPress={onHapticRefresh}
              >
                {CONTENT_CODES().HOME.SYNC}
              </Button>
            </Box>
          </HasAnyHealthPermissionsWrapper>
          <ToggleButton
            icon='cog'
            iconColor={theme.colors.primary}
            status='unchecked'
            value='cog'
            onPress={() => to.settingsScreen()}
          />
        </Box>
      </Box>

      <ScreenContent>
        <HomeContentMemo />
      </ScreenContent>
    </ScreenWrapper>
  );
};
