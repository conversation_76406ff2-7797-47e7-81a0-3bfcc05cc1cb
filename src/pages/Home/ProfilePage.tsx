import {Images} from '@assets';
import {Box, Icon, IconButton, LoadingIndicator, Surface, Text} from '@base-components';
import {CopyClipboardWrapper, FullHealthSyncReload, ScreenContent, ScreenHeader} from '@components';
import {CONTENT_CODES, DOMAIN_CONSTANTS} from '@constants';
import {
  ScreenWrapper,
  useAppUserById,
  useAppUserMetadataHealthData,
  useAppUserSafe,
  useProfileActionSheet,
} from '@contexts';
import {useProfileParams} from '@navigation';
import {timestampToDate} from '@types';
import {
  formatDisplayDayMonthYear,
  formatDisplayMiles,
  formatDisplaySteps100k,
  formatHeightInFeet,
  formatPhoneNumber,
  getLatestGoalsOrDefault,
} from '@utils';

// eslint-disable-next-line max-lines-per-function -- all components configured differently, no reuse possible
export const ProfilePage: React.FC = () => {
  const {userId} = useProfileParams();
  const appUser = useAppUserById(userId);
  const authUser = useAppUserSafe();
  const canViewAccountInfo = userId === authUser.id;
  const {mutateAsync: onPress} = useProfileActionSheet(authUser);
  const {dailyGoalMileageInMeters, dailyGoalStepCount} = getLatestGoalsOrDefault(appUser);
  const {totalDistanceMeters = 0, totalStepsCount = 0} = useAppUserMetadataHealthData(userId) ?? {};

  return (
    <ScreenWrapper>
      <ScreenHeader
        right={canViewAccountInfo && <IconButton icon='dots-vertical' onPress={() => onPress()} />}
        title={CONTENT_CODES().VIEW_PROFILE.HEADER}
      />

      <ScreenContent>
        {!appUser && <LoadingIndicator />}
        {appUser && (
          <Box alignItems='center'>
            <Images.defaultProfile
              overrideSource={appUser.profilePicture}
              style={{width: 96, height: 96, borderRadius: 48, marginHorizontal: 'auto'}}
            />
            <Text pt={1} style={{fontSize: 28}} variant='titleLarge'>
              {appUser.firstName} {appUser.lastName}
            </Text>

            {canViewAccountInfo && (
              <Box py={1} width='100%'>
                <Text pb={2} variant='labelLarge'>
                  {CONTENT_CODES().VIEW_PROFILE.HEALTH_DATA_LABEL}
                </Text>
                <Surface borderRadius={10} p={2}>
                  <Text variant='labelMedium'>Total Distance</Text>
                  <Text variant='bodyMedium'>{formatDisplayMiles(totalDistanceMeters)} miles</Text>
                  <Box py={1} />
                  <Text variant='labelMedium'>Total Steps</Text>
                  <Text variant='bodyMedium'>{formatDisplaySteps100k(totalStepsCount)}</Text>
                  <FullHealthSyncReload />
                </Surface>
              </Box>
            )}

            <Box py={1} width='100%'>
              <Text pb={2} variant='labelLarge'>
                {CONTENT_CODES().VIEW_PROFILE.CONTACT_INFO_LABEL}
              </Text>
              <Surface borderRadius={10} p={2}>
                <Text variant='labelMedium'>Email</Text>
                <Text variant='bodyMedium'>{appUser.email}</Text>
                <Box py={1} />
                <Text variant='labelMedium'>Phone</Text>
                <Text variant='bodyMedium'>
                  {appUser.phoneNumber ? formatPhoneNumber(appUser.phoneNumber) : '-'}
                </Text>
              </Surface>
            </Box>

            {canViewAccountInfo && (
              <Box py={1} width='100%'>
                <Text pb={2} variant='labelLarge'>
                  {CONTENT_CODES().VIEW_PROFILE.PROFILE_INFO_LABEL}
                </Text>

                <Surface borderRadius={10} p={2}>
                  {appUser.heightInInches !== undefined && (
                    <>
                      <Text variant='labelMedium'>Height</Text>
                      <Text variant='bodyMedium'>{formatHeightInFeet(appUser.heightInInches)}</Text>
                      <Box py={1} />
                    </>
                  )}

                  <Text variant='labelMedium'>
                    {CONTENT_CODES().VIEW_PROFILE.STEP_LENGTH_LABEL}
                  </Text>
                  <Text variant='bodyMedium'>
                    {appUser.stepLength === undefined
                      ? CONTENT_CODES().VIEW_PROFILE.STEP_LENGTH_DEFAULT_UNIT_LABEL(
                          DOMAIN_CONSTANTS().DEFAULT_STEP_LENGTH_INCHES,
                        )
                      : CONTENT_CODES().VIEW_PROFILE.STEP_LENGTH_UNIT_LABEL(appUser.stepLength)}
                  </Text>

                  <Box py={1} />

                  <Text variant='labelMedium'>
                    {CONTENT_CODES().VIEW_PROFILE.DISTANCE_SOURCE_LABEL}
                  </Text>
                  <Text variant='bodyMedium'>
                    {appUser.isMileageGpsSourced
                      ? CONTENT_CODES().VIEW_PROFILE.TRUE_LABEL
                      : CONTENT_CODES().VIEW_PROFILE.FALSE_LABEL_DEFAULT}
                  </Text>

                  <Box py={1} />

                  <CopyClipboardWrapper text={appUser.id}>
                    <Box alignItems='center' flexDirection='row'>
                      <Text pr={1} variant='labelMedium'>
                        ID
                      </Text>
                      <Icon name='content-copy' size={12} />
                    </Box>
                  </CopyClipboardWrapper>
                  <Text variant='bodyMedium'>{appUser.id}</Text>

                  <Box py={1} />

                  <Text variant='labelMedium'>Joined</Text>
                  <Text variant='bodyMedium'>
                    {formatDisplayDayMonthYear(timestampToDate(appUser.accountCreatedDateTime))}
                  </Text>
                </Surface>
              </Box>
            )}

            <Box py={1} width='100%'>
              <Text pb={2} variant='labelLarge'>
                {CONTENT_CODES().VIEW_PROFILE.GOALS_LABEL}
              </Text>
              <Surface borderRadius={10} p={2}>
                <Text variant='labelMedium'>Daily Mileage Goal</Text>
                <Text variant='bodyMedium'>{formatDisplayMiles(dailyGoalMileageInMeters)}</Text>
                <Box py={1} />
                <Text variant='labelMedium'>Daily Step Count Goal</Text>
                <Text variant='bodyMedium'>{formatDisplaySteps100k(dailyGoalStepCount)}</Text>
              </Surface>
            </Box>
          </Box>
        )}
      </ScreenContent>

      <Box py={2} />
    </ScreenWrapper>
  );
};
