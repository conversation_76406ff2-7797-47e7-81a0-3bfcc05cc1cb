import {openSettings} from 'expo-linking';
import {Box, Button, Divider, Text, TextLink} from '@base-components';
import {HelpIcon, RenderError, ScreenContent, ScreenHeader} from '@components';
import {
  CONTENT_CODES,
  DEV_FEATURE_FLAGS,
  DOMAIN_CONSTANTS,
  isIos,
  nativeAppVersion,
  nativeBuildVersion,
} from '@constants';
import {
  handleEnableBackgroundHealthSync,
  ScreenWrapper,
  useAppUserSafe,
  useHasAllHealthPermissions,
  useIsCoach,
  useLogoutMutation,
} from '@contexts';
import {
  onLinkToFeedbackApp,
  onLinkToFlyFitFAQ,
  onLinkToHealthApp,
  onLinkToHelp,
  onLinkToPrivacyPolicy,
  useLinkTo,
} from '@navigation';
import {
  clearAllStorage,
  expoUpdatesReleaseChannel,
  expoUpdatesUpdateId,
  fetchExpoUpdate,
  getDeviceInfo,
  isExpoEmbedLaunch,
  LOGGER,
  reloadApp,
} from '@utils';

const AccountActions: React.FC = () => {
  const logoutMutation = useLogoutMutation();
  const to = useLinkTo();
  const {id} = useAppUserSafe();
  const isCoach = useIsCoach();

  return (
    <Box pb={2}>
      <Text variant='labelLarge'>{CONTENT_CODES().SETTINGS.ACCOUNT_ACTIONS.LABEL}</Text>
      <Divider mb={1} style={{width: '100%'}} />
      <Button icon='account' mb={2} mode='outlined' onPress={() => to.profile({userId: id})}>
        {CONTENT_CODES().SETTINGS.ACCOUNT_ACTIONS.VIEW_PROFILE}
      </Button>
      <Button
        icon='bell-outline'
        mb={2}
        mode='outlined'
        onPress={() => to.notificationSettingsScreen()}
      >
        {CONTENT_CODES().SETTINGS.ACCOUNT_ACTIONS.NOTIFICATION_SETTINGS}
      </Button>
      <Button icon='sync' mb={2} mode='outlined' onPress={() => to.healthSyncSettings()}>
        {CONTENT_CODES().SETTINGS.ACCOUNT_ACTIONS.HEALTH_SYNC_SETTINGS}
      </Button>
      {isCoach && (
        <Button
          icon='file-document-multiple-outline'
          mb={2}
          mode='outlined'
          onPress={() => to.summaryReports()}
        >
          {CONTENT_CODES().SETTINGS.ACCOUNT_ACTIONS.SUMMARY_REPORTS}
        </Button>
      )}
      <Button
        disabled={logoutMutation.isPending}
        icon='door-closed'
        loading={logoutMutation.isPending}
        mb={2}
        mode='outlined'
        onPress={() => logoutMutation.mutateAsync()}
      >
        {CONTENT_CODES().SETTINGS.ACCOUNT_ACTIONS.LOG_OUT}
      </Button>
    </Box>
  );
};

const AppSettings: React.FC = () => {
  const {data: isHealthSyncEnabled} = useHasAllHealthPermissions();
  // TODO: add back backround health refresh
  const showBackroundRefresh = (false as boolean) && isHealthSyncEnabled;

  return (
    <Box pb={2}>
      <Text variant='labelLarge'>{CONTENT_CODES().SETTINGS.APP_SETTINGS.LABEL}</Text>

      <Divider mb={1} style={{width: '100%'}} />

      {showBackroundRefresh && (
        <Button icon='table-sync' mb={2} mode='outlined' onPress={handleEnableBackgroundHealthSync}>
          {CONTENT_CODES().SETTINGS.APP_SETTINGS.BACKGROUND_REFRESH}
        </Button>
      )}
      <Button icon='cog' mb={2} mode='outlined' onPress={openSettings}>
        {CONTENT_CODES().SETTINGS.APP_SETTINGS.APP_SETTINGS}
      </Button>
      <Button icon={isIos ? 'apple' : 'android'} mb={2} mode='outlined' onPress={onLinkToHealthApp}>
        {CONTENT_CODES().SETTINGS.APP_SETTINGS.HEALTH_APP_LINK}
      </Button>

      <Button
        icon='archive-cancel'
        mb={2}
        mode='outlined'
        onPress={async () => {
          LOGGER.debug('[Debug] Clearing Device Local Storage');
          clearAllStorage();
          await reloadApp();
        }}
      >
        {CONTENT_CODES().SETTINGS.DEBUG_ACTIONS.RESET_DEVICE_LOCAL_STORAGE}
      </Button>
    </Box>
  );
};

const ResourcesAndContact: React.FC = () => (
  <Box pb={2}>
    <Text variant='labelLarge'>{CONTENT_CODES().SETTINGS.RESOURCES_AND_CONTACT.LABEL}</Text>

    <Divider mb={1} style={{width: '100%'}} />

    <Button icon='chat-question-outline' mb={2} mode='outlined' onPress={onLinkToFlyFitFAQ}>
      {CONTENT_CODES().SETTINGS.RESOURCES_AND_CONTACT.FLY_FIT_FAQ_LABEL}
    </Button>
    <Button icon='email-send-outline' mb={2} mode='outlined' onPress={onLinkToHelp}>
      {CONTENT_CODES().MISC.HELP_SUPPORT_LABEL}
    </Button>
    <Button icon='account-heart-outline' mb={2} mode='outlined' onPress={onLinkToFeedbackApp}>
      {CONTENT_CODES().SETTINGS.RESOURCES_AND_CONTACT.FEEDBACK_APP_LABEL}
    </Button>
  </Box>
);

const AppInfoAndLegal: React.FC = () => (
  <>
    <Text variant='labelMedium'>{CONTENT_CODES().SETTINGS.APP_INFO.LABEL}</Text>

    <Text>
      {CONTENT_CODES().SETTINGS.APP_INFO.APP_VERSION_LABEL}: {nativeAppVersion}
    </Text>
    <Text>
      {CONTENT_CODES().SETTINGS.APP_INFO.BUILD_VERSION_LABEL}: {nativeBuildVersion}
    </Text>
    <Text>
      {CONTENT_CODES().SETTINGS.APP_INFO.RELEASE_CHANNEL_LABEL}:{' '}
      {expoUpdatesReleaseChannel ?? 'default'}
    </Text>
    <Text>
      {CONTENT_CODES().SETTINGS.APP_INFO.UPDATE_ID_LABEL}: {expoUpdatesUpdateId ?? 'default'}
    </Text>
    <Text>
      {CONTENT_CODES().SETTINGS.APP_INFO.EMBED_LAUNCH_LABEL}:{' '}
      {isExpoEmbedLaunch ? 'built-in' : 'over-the-air'}
    </Text>

    <Text pt={2} variant='labelMedium'>
      {CONTENT_CODES().SETTINGS.APP_INFO.LEGAL_SECTION_LABEL}
    </Text>
    <TextLink onPress={onLinkToPrivacyPolicy}>
      {CONTENT_CODES().SETTINGS.APP_INFO.PRIVACY_POLICY_LABEL}
    </TextLink>
    <Text>{CONTENT_CODES().SETTINGS.APP_INFO.COPYRIGHT_LABEL}</Text>
    <Box pt={2} />
  </>
);

const DebugBox: React.FC = () => (
  <Box pb={2}>
    <Text variant='labelLarge'>{CONTENT_CODES().SETTINGS.DEBUG_ACTIONS.LABEL}</Text>
    <Divider mb={1} style={{width: '100%'}} />
    <RenderError />
    <Button
      icon='printer'
      mode='outlined'
      mt={2}
      onPress={async () => {
        LOGGER.debug('Device Info', await getDeviceInfo());
      }}
    >
      {CONTENT_CODES().SETTINGS.APP_SETTINGS.LOG_DEVICE_INFO}
    </Button>
    {(false as boolean) && (
      <Button icon='update' mb={2} mode='outlined' onPress={fetchExpoUpdate}>
        {CONTENT_CODES().SETTINGS.APP_SETTINGS.CHECK_FOR_UPDATES}
      </Button>
    )}
  </Box>
);

export const SettingScreen: React.FC = () => {
  const {email} = useAppUserSafe();
  const isShowDebug =
    DEV_FEATURE_FLAGS().isDebugViewEnabled ||
    DOMAIN_CONSTANTS().SPECIAL_USERS.DEBUG_USERS.includes(email);

  return (
    <ScreenWrapper>
      <ScreenHeader right={<HelpIcon />} title='Settings' />

      <ScreenContent>
        <AccountActions />

        <AppSettings />

        <ResourcesAndContact />

        {isShowDebug && <DebugBox />}

        <Box pt={4} />

        <AppInfoAndLegal />
      </ScreenContent>
    </ScreenWrapper>
  );
};
