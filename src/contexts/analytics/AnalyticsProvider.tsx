// eslint-disable-next-line @typescript-eslint/no-restricted-imports -- for analytics context
import {createContext, type ReactNode, useContext, useEffect} from 'react';
import {isProd} from '@constants';
import type {AnalyticsService} from './analyticsService';
import {consoleAnalytics} from './consoleAnalytics';
import {firebaseAnalyticsService} from './firebaseAnalytics';
import {useAppUser} from '../authContext';

// Default to console analytics
const defaultService = consoleAnalytics();
// eslint-disable-next-line @typescript-eslint/naming-convention -- context naming convention
const AnalyticsContext = createContext<AnalyticsService>(defaultService);

export const useAnalytics = () => useContext(AnalyticsContext);

export const AnalyticsProvider: React.FC<{children: ReactNode}> = ({children}) => {
  // This can be changed to determine provider from Expo config extra.analyticsProvider
  const service: AnalyticsService = isProd ? firebaseAnalyticsService() : defaultService;
  const appUser = useAppUser();

  useEffect(() => {
    if (!appUser) return;

    void service.setUserId(appUser.id);
    void service.setUserProperties({
      email: appUser.email,
      user_type: appUser.type,
      phone_number: appUser.phoneNumber ?? 'none',
      time_zone: appUser.timeZone ?? Intl.DateTimeFormat().resolvedOptions().timeZone,
      tracking_device: appUser.trackingDeviceType ?? 'none',
      tracking_app: appUser.trackingApp ?? 'none',
      os: appUser.analytics?.deviceInfo?.osName ?? 'none',
      os_version: appUser.analytics?.deviceInfo?.osVersion ?? 'none',
      phone_brand: appUser.analytics?.deviceInfo?.brand ?? 'none',
      phone_model: appUser.analytics?.deviceInfo?.modelName ?? 'none',
    });

  }, [appUser, service]);

  return <AnalyticsContext.Provider value={service}>{children}</AnalyticsContext.Provider>;
};
