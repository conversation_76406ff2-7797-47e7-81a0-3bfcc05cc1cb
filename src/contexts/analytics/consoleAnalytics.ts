import type {AnalyticsService} from './analyticsService';

export const consoleAnalytics = (): AnalyticsService => ({
  logEvent: async (name: string, params?: Record<string, unknown>) => {
    // eslint-disable-next-line no-console -- analytics
    console.log(`[Analytics][event] ${name}`, params);
  },

  setCurrentScreen: async (screenName: string) => {
    // eslint-disable-next-line no-console -- analytics
    console.log(`[Analytics][screen] ${screenName}`);
  },

  setUserId: async (id) => {
    console.log('[Analytics][userId]', id);
  },

  setUserProperties: async (props) => {
    console.log('[Analytics][userProperties]', props);
  },
});
