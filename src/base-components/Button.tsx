import {Button as ButtonPaper} from 'react-native-paper';
import {styled} from 'styled-components/native';
import {compose, flex, layout, position, space} from 'styled-system';
import type {ButtonProps as ButtonPaperProps} from 'react-native-paper';
import type {FlexboxProps, LayoutProps, PositionProps, SpaceProps} from 'styled-system';
import {type StyledTheme} from '@utils';
import {Box, type BoxProps} from './Box';

type ButtonProps = SpaceProps<StyledTheme> &
  FlexboxProps<StyledTheme> &
  PositionProps<StyledTheme> &
  LayoutProps<StyledTheme>;

export const Button = styled(ButtonPaper)<ButtonProps>(compose(layout, flex, position, space));

type ButtonCenteredProps = ButtonPaperProps & {
  boxProps?: BoxProps;
};

export const ButtonCentered: React.FC<ButtonCenteredProps> = ({boxProps, ...props}) => (
  <Box flexDirection='row' justifyContent='center' {...boxProps}>
    <ButtonPaper mode='outlined' {...props} />
  </Box>
);

// type Button2Props = ButtonProps & Pick<ButtonPaperProps, 'mode' | 'icon'> &
// {onPress: () => void; children: string};

// export const Button2: React.FC<Button2Props> = ({mode, onPress, children, ...props}) => {
//   const theme = useAppTheme();

//   const bgColor = mode === 'contained' ? theme.colors.primary : 'transparent';

//   return (
//     <TouchableHighlight onPress={onPress}>
//       <Box {...props} >{children}</Box>
//     </TouchableHighlight>
//   );

// }
