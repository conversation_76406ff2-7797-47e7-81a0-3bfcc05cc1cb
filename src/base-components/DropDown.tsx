/**
 * SOURCED FROM LIBRARY CODE: https://github.com/fateh999/react-native-paper-dropdown/blob/master/src/DropDown.tsx
 */

/* eslint-disable max-lines-per-function, react/display-name -- library code */
import {forwardRef, Fragment, useCallback, useEffect, useState} from 'react';
import {ScrollView, View} from 'react-native';
import {Checkbox, Divider, Menu, TextInput, TouchableRipple, useTheme} from 'react-native-paper';
import type {LayoutChangeEvent, TextStyle, ViewStyle} from 'react-native';
import type {MD3Theme as Theme, TextInputProps} from 'react-native-paper';
import {useAppTheme} from '@utils';
import {Box} from './Box';
import {TextInputPaper} from './TextInput';

type Without<T, K> = Pick<T, Exclude<keyof T, K>>;

type DropDownPropsInterface<V extends string> = {
  accessibilityLabel?: string;
  activeColor?: string;
  dropDownContainerHeight?: number;
  dropDownContainerMaxHeight?: number;
  dropDownItemSelectedStyle?: ViewStyle;
  dropDownItemSelectedTextStyle?: TextStyle;
  dropDownItemStyle?: ViewStyle;
  dropDownItemTextStyle?: TextStyle;
  dropDownStyle?: ViewStyle;
  inputProps?: TextInputPropsWithoutTheme;
  label?: string | undefined;
  list: {
    custom?: React.ReactNode;
    label: string;
    value: V;
  }[];
  mode?: 'outlined' | 'flat' | undefined;
  multiSelect?: boolean | undefined;
  onDismiss: () => void;
  placeholder?: string | undefined;
  setValue: (_value: V) => void;
  showDropDown: () => void;
  theme?: Theme;
  value: V;
  visible: boolean;
};

type TextInputPropsWithoutTheme = Without<TextInputProps, 'theme'>;

export const DropDown = forwardRef<View, DropDownPropsInterface<string>>((props, ref) => {
  const activeTheme = useTheme();
  const {
    accessibilityLabel,
    activeColor,
    dropDownContainerHeight,
    dropDownContainerMaxHeight,
    dropDownItemSelectedStyle,
    dropDownItemSelectedTextStyle,
    dropDownItemStyle,
    dropDownItemTextStyle,
    dropDownStyle,
    inputProps,
    label,
    list,
    mode,
    multiSelect = false,
    onDismiss,
    placeholder,
    setValue,
    showDropDown,
    theme,
    value,
    visible,
  } = props;
  const [displayValue, setDisplayValue] = useState('');
  const [inputLayout, setInputLayout] = useState({
    height: 0,
    width: 0,
    x: 0,
    y: 0,
  });

  const onLayout = (event: LayoutChangeEvent) => {
    setInputLayout(event.nativeEvent.layout);
  };

  useEffect(() => {
    if (multiSelect) {
      const _labels = list
        .filter(_ => value.includes(_.value))
        .map(_ => _.label)
        .join(', ');
      setDisplayValue(_labels);
    } else {
      const _label = list.find(_ => _.value === value)?.label;
      if (_label) {
        setDisplayValue(_label);
      }
    }
  }, [list, multiSelect, value]);

  const isActive = useCallback(
    (currentValue: string) => {
      if (multiSelect) {
        return value.includes(currentValue);
      }

      return value === currentValue;
    },
    [multiSelect, value],
  );

  const setActive = useCallback(
    (currentValue: string) => {
      if (multiSelect) {
        const valueIndex = value.indexOf(currentValue);
        const values = value.split(',');
        if (valueIndex === -1) {
          setValue([...values, currentValue].join(','));
        } else {
          setValue([...values].filter(_value => _value !== currentValue).join(','));
        }
      } else {
        setValue(currentValue);
      }
    },
    [multiSelect, setValue, value],
  );

  return (
    // @ts-ignore -- library error
    <Menu
      anchor={
        <TouchableRipple
          ref={ref}
          accessibilityLabel={accessibilityLabel}
          onLayout={onLayout}
          onPress={showDropDown}
        >
          <View pointerEvents='none' style={{minWidth: '100%'}}>
            <TextInput
              label={label ?? ''}
              mode={mode ?? 'outlined'}
              placeholder={placeholder ?? ''}
              pointerEvents='none'
              right={<TextInput.Icon icon={visible ? 'menu-up' : 'menu-down'} />}
              theme={theme!}
              value={displayValue}
              {...inputProps}
            />
          </View>
        </TouchableRipple>
      }
      style={{
        maxWidth: inputLayout.width,
        width: inputLayout.width,
        marginTop: -10,
        // marginTop: inputLayout.height, // commented out ever since select fields show on modals
        ...dropDownStyle,
      }}
      theme={theme}
      visible={visible}
      onDismiss={onDismiss}
    >
      <ScrollView
        bounces={false}
        style={{
          ...(dropDownContainerHeight
            ? {
                height: dropDownContainerHeight,
              }
            : {
                maxHeight: dropDownContainerMaxHeight ?? 200,
              }),
        }}
      >
        {list.map((_item, _index) => (
          <Fragment key={_item.value}>
            <TouchableRipple
              style={{
                flexDirection: 'row',
                alignItems: 'center',
              }}
              onPress={() => {
                setActive(_item.value);
                onDismiss();
              }}
            >
              <Fragment>
                <Menu.Item
                  style={{
                    flex: 1,
                    maxWidth: inputLayout.width,
                    ...(isActive(_item.value) ? dropDownItemSelectedStyle : dropDownItemStyle),
                  }}
                  title={_item.custom ?? _item.label}
                  titleStyle={{
                    color: isActive(_item.value)
                      ? (activeColor ?? (theme ?? activeTheme).colors.primary)
                      : (theme ?? activeTheme).colors.primary,
                    ...(isActive(_item.value)
                      ? dropDownItemSelectedTextStyle
                      : dropDownItemTextStyle),
                  }}
                />
                {multiSelect && (
                  <Checkbox.Android
                    status={isActive(_item.value) ? 'checked' : 'unchecked'}
                    theme={{
                      colors: {accent: activeTheme.colors.primary},
                    }}
                    onPress={() => setActive(_item.value)}
                  />
                )}
              </Fragment>
            </TouchableRipple>
            <Divider />
          </Fragment>
        ))}
      </ScrollView>
    </Menu>
  );
});
/* eslint-enable max-lines-per-function, react/display-name */

type LabelOptions = {
  defaultLabel: number;
  end: number;
  formatLabel?: (key: number) => string;
  interval: number;
  start: number;
  transformValue: (value: number) => string;
};

export const generateDropdownOptions = (options: LabelOptions) => {
  const {defaultLabel, end, formatLabel, interval, start, transformValue} = options;

  return Array.from(
    {length: Math.floor((end - start) / interval) + 1},
    (_, i) => start + (i * interval),
  ).map(currentValue => {
    const label = `${formatLabel ? formatLabel(currentValue) : currentValue} ${currentValue === defaultLabel ? '(default)' : ''}`;
    const value = transformValue(currentValue);

    return {label, value};
  });
};

type DropDownSelfContainedProps<V extends string> = Omit<
  DropDownPropsInterface<V>,
  'onDismiss' | 'visible' | 'showDropDown' | 'theme' | 'mode'
>;

export const DropDownSelfContained = <V extends string>(props: DropDownSelfContainedProps<V>) => {
  const [isOpen, setIsOpen] = useState(false);
  const theme = useAppTheme();

  return (
    <Box style={{flexDirection: 'row'}}>
      {/* @ts-ignore -- allowed dynamically typed value */}
      <DropDown
        inputProps={{
          right: (
            <TextInputPaper.Icon icon={`arrow-${isOpen ? 'up' : 'down'}-drop-circle-outline`} />
          ),
        }}
        mode='outlined'
        showDropDown={() => setIsOpen(true)}
        theme={theme}
        visible={isOpen}
        onDismiss={() => setIsOpen(false)}
        {...props}
      />
    </Box>
  );
};
