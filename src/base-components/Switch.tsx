import {Switch as PaperSwitch} from 'react-native-paper';
import type {SwitchProps as PaperSwitchProps} from 'react-native-paper';
import {isString} from '@utils';
import {Box} from './Box';
import {Text} from './Text';

type SwitchProps = {
  label?: React.ReactNode;
  value: boolean | undefined;
  valueLabelFalse: React.ReactNode;
  valueLabelTrue: React.ReactNode;
} & Omit<PaperSwitchProps, 'value'>;

export const Switch: React.FC<SwitchProps> = ({
  label,
  value,
  valueLabelFalse,
  valueLabelTrue,
  ...rest
}) => {
  const valueLabel = value ? valueLabelTrue : valueLabelFalse;

  return (
    <>
      {label && (
        <Text pb={1} variant='bodyMedium'>
          {label}
        </Text>
      )}
      <Box alignItems='center' flexDirection='row' pb={1}>
        <PaperSwitch value={value ?? false} {...rest} />
        {isString(valueLabelFalse) && isString(valueLabelTrue) ? (
          <Text maxWidth={250} pl={1} textAlign='center' variant='bodyMedium'>
            {valueLabel}
          </Text>
        ) : (
          valueLabel
        )}
      </Box>
    </>
  );
};
