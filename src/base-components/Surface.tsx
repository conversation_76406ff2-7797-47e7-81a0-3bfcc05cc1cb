import {Surface as SurfacePaper} from 'react-native-paper';
import {styled} from 'styled-components/native';
import {border, compose, flexbox, layout, position, space} from 'styled-system';
import type {
  BorderProps,
  FlexboxProps,
  LayoutProps,
  PositionProps,
  SpaceProps,
} from 'styled-system';
import type {StyledTheme} from '@utils';

type SurfaceProps = SpaceProps<StyledTheme> &
  PositionProps<StyledTheme> &
  LayoutProps<StyledTheme> &
  FlexboxProps<StyledTheme> &
  BorderProps<StyledTheme>;

export const Surface = styled(SurfacePaper)<SurfaceProps>(
  compose(space, layout, position, flexbox, border),
);
