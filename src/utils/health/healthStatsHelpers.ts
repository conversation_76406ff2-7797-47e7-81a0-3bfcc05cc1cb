import {isWithinInterval} from 'date-fns';
import type {HealthStats, HealthStatsDates, IsoDate, RawTimestamp, TimeZones} from '@types';
import {
  anyTimestampToFirestoreClientTimestamp,
  getDayIntervalForIsoDate,
  timestampToDate,
} from '@types';
import {getIsoStringFromTimestamp, metersToMiles} from '../dates-timing-conversion';
import {arrayPartition, combineArrays, createIsArraySuperSet} from '../primitives';

export const healthStatsDatesGroupedByDay = (stats: HealthStats[], timeZone: TimeZones) =>
  stats.reduce<Map<IsoDate, HealthStatsDates & {isoDate: IsoDate}>>((acc, stat) => {
    const isoDateKey = getIsoStringFromTimestamp(stat.startDate, timeZone);

    if (acc.has(isoDateKey)) {
      const existingStat = acc.get(isoDateKey);
      if (existingStat) {
        existingStat.distanceMeters =
          (existingStat.distanceMeters ?? 0) + (stat.distanceMeters ?? 0);
        existingStat.stepsCount = (existingStat.stepsCount ?? 0) + (stat.stepsCount ?? 0);
        existingStat.exerciseMinutes =
          (existingStat.exerciseMinutes ?? 0) + (stat.exerciseMinutes ?? 0);
      }
    } else {
      const interval = getDayIntervalForIsoDate(isoDateKey, timeZone);
      acc.set(isoDateKey, {
        ...stat,
        isoDate: isoDateKey,
        startDate: interval.startDate,
        endDate: interval.endDate,
      });
    }

    return acc;
  }, new Map<IsoDate, HealthStatsDates & {isoDate: IsoDate}>());

export const healthStatsRawToFirebaseTimestamps = (stat: HealthStats) => ({
  ...stat,
  startDate: anyTimestampToFirestoreClientTimestamp(stat.startDate),
  endDate: anyTimestampToFirestoreClientTimestamp(stat.endDate),
});

const getHealthStatId = (stat: HealthStats) =>
  `${timestampToDate(stat.startDate).getTime()}-${timestampToDate(stat.endDate).getTime()}-${stat.distanceMeters}-${stat.stepsCount}-${stat.isDistanceGpsSourced}-${stat.isOverrideEntry}`;

const getHealthStatDateOnlyId = (stat: HealthStats) =>
  `${timestampToDate(stat.startDate).getTime()}-${timestampToDate(stat.endDate).getTime()}`;

// Function to compare two HealthStats objects for equality
export const isEqualHealthStat = (a: HealthStats, b: HealthStats) =>
  getHealthStatId(a) === getHealthStatId(b);

const isEqualHealthStatDateOnly = (a: HealthStats, b: HealthStats) =>
  getHealthStatDateOnlyId(a) === getHealthStatDateOnlyId(b);

export const getIsChangeInHeightStats = (
  original: HealthStats[] | undefined,
  updates: HealthStats[],
) => !createIsArraySuperSet(getHealthStatId)(original ?? [], updates);

/**
 * Sorts the health stats in descending order (newest stats are first)
 */
const sortHealthStatsReverse = (a: HealthStats, b: HealthStats) =>
  timestampToDate(b.startDate).getTime() - timestampToDate(a.startDate).getTime();
export const sortHealthStats = (a: HealthStats, b: HealthStats) =>
  timestampToDate(a.startDate).getTime() - timestampToDate(b.startDate).getTime();

type StatWithInterval = {endDate: RawTimestamp; startDate: RawTimestamp};

/**
 * Check if the given timestamp is within/overlapping the given stat date interval
 *
 * @param date the timestamp to check
 * @param stat the health stat containing the date interval
 * @returns true if the timestamp is within the date interval, false otherwise
 */
const isDateWithinStatInterval = (date: RawTimestamp, stat: StatWithInterval) =>
  isWithinInterval(timestampToDate(date), {
    start: timestampToDate(stat.startDate),
    end: timestampToDate(stat.endDate),
  });

export const isDateWithinRangeLoose = (source: StatWithInterval, range: StatWithInterval) =>
  isDateWithinStatInterval(source.startDate, range) ||
  isDateWithinStatInterval(source.endDate, range);

const getUpdatesNonOverlappingWithOverrides = (
  originalOverrides: HealthStats[],
  updates: HealthStats[],
) =>
  arrayPartition(updates, update => {
    const isOverlappingOveride = originalOverrides.some(override =>
      isDateWithinRangeLoose(update, {startDate: override.startDate, endDate: override.endDate}),
    );
    // Keep only updates that do not overlap with any override
    return !isOverlappingOveride;
  });

export const mergeHealthStatsWithOverrides = (
  original: HealthStats[],
  updates: HealthStats[],
): [HealthStats[], HealthStats[]] => {
  // Filter out overrides for special handling
  const [originalOverrides, originalNonOverrides] = arrayPartition(
    original,
    stat => !!stat.isOverrideEntry,
  );
  const [updateOverrides, updateNonOverrides] = arrayPartition(
    updates,
    stat => !!stat.isOverrideEntry,
  );

  // Exclude any overrides that overlap with any update that is an override
  const [originalOverridesExcludingUpdateOverrides, removedOriginalOverrides] =
    getUpdatesNonOverlappingWithOverrides(updateOverrides, originalOverrides);

  // Combine both original and update overrides
  const allOverrides = combineArrays(originalOverridesExcludingUpdateOverrides, updateOverrides);

  // Filter out any original non-override stats that overlap with update overrides
  // This ensures override entries completely replace any overlapping stats
  const [originalNonOverridesNotOverlapped, removedOriginalNonOverrides] =
    getUpdatesNonOverlappingWithOverrides(updateOverrides, originalNonOverrides);

  // Exclude updates that overlap with any override period
  const [updatesNonOverlappingWithOverrides, removedUpdatesOverlappingWithOverrides] =
    getUpdatesNonOverlappingWithOverrides(allOverrides, updateNonOverrides);

  // Merge non-override original and non-overlapping updates stats
  const removedUpdatesFromMerge = [] as HealthStats[];
  const mergedAndHandledOverlaps = updatesNonOverlappingWithOverrides.reduce((acc, update) => {
    // Check if the update already exists in the original array
    const originalStatOverlappingUpdateIndex = acc.findIndex(stat =>
      isEqualHealthStatDateOnly(stat, update),
    );
    // If the update does not exist in the original array
    if (originalStatOverlappingUpdateIndex === -1) {
      // Add the update to the merged array
      acc.push(update);
      return acc;
    }
    // If the update already exists in the original array
    // Overwrite the original with the update
    acc[originalStatOverlappingUpdateIndex] = update;
    return acc;
  }, originalNonOverridesNotOverlapped);

  // Merge the overrides and the non-overlapping updates
  const allUpdates = combineArrays(allOverrides, mergedAndHandledOverlaps)
    .sort(sortHealthStatsReverse)
    // Keep only data with at least one of the fields populated
    .filter(stat => !!stat.stepsCount || !!stat.distanceMeters || !!stat.exerciseMinutes);

  // Combine all of the removed stats
  const allRemoved = combineArrays(
    removedOriginalOverrides,
    removedOriginalNonOverrides,
    removedUpdatesOverlappingWithOverrides,
    removedUpdatesFromMerge,
  );

  // Return both the updates and the removed stats
  return [allUpdates, allRemoved];
};

export const getSumSteps = (stats: {stepsCount?: number | undefined }[]) =>
  stats.reduce((acc, stat) => acc + (stat.stepsCount ?? 0), 0);

export const getSumDistanceMeters = (stats: {distanceMeters?: number | undefined}[]) =>
  stats.reduce((acc, stat) => acc + (stat.distanceMeters ?? 0), 0);

export const getSumMileage = (stats: {distanceMeters?: number | undefined}[]) => {
  const sumMeters = getSumDistanceMeters(stats);
  return metersToMiles(sumMeters);
};

export const formatDisplayMiles = (meters: number | undefined, fractionDigits = 2) => {
  const miles = metersToMiles(meters ?? 0);
  const roundedMiles = miles.toFixed(fractionDigits); // Ensures fixed decimal places with rounding
  return Number(roundedMiles).toLocaleString('en-US', {
    minimumFractionDigits: fractionDigits,
    maximumFractionDigits: fractionDigits,
  });
};

export const getSumDisplayMiles = (stats: {distanceMeters?: number | undefined}[]) => {
  const sumMeters = getSumDistanceMeters(stats);
  return formatDisplayMiles(sumMeters);
};

const formatDisplayStepsThreshold = (steps: number | undefined, threshold: number): string => {
  const roundedSteps = Math.round(steps ?? 0);

  // Only format with k only if over threshold
  if (roundedSteps >= threshold) {
    const stepsInK = Math.floor(roundedSteps / 1000);
    return stepsInK >= 1000 ? `${stepsInK.toLocaleString('en-US')}k` : `${stepsInK}k`;
  }

  return roundedSteps.toLocaleString('en-US');
};

export const formatDisplaySteps100k = (steps: number | undefined): string =>
  formatDisplayStepsThreshold(steps, 100_000);

export const formatDisplaySteps10k = (steps: number | undefined): string =>
  formatDisplayStepsThreshold(steps, 10_000);
