# [1.21.0](https://github.com/FlyBodies/fly-fit/compare/v1.20.23...v1.21.0) (2025-05-27)


### Features

* Analytics wrapper with Firebase integration  ([#247](https://github.com/FlyBodies/fly-fit/issues/247)) ([3b86ff2](https://github.com/FlyBodies/fly-fit/commit/3b86ff2694d5223febc3c34bd1bb19d87e364f32))

## [1.20.23](https://github.com/FlyBodies/fly-fit/compare/v1.20.22...v1.20.23) (2025-05-26)


### Bug Fixes

* Add sign up logging to a google sheet ([#248](https://github.com/FlyBodies/fly-fit/issues/248)) ([7705640](https://github.com/FlyBodies/fly-fit/commit/77056408504b76499b3c8da83b0e3dd91acaf73b))

## [1.20.22](https://github.com/FlyBodies/fly-fit/compare/v1.20.21...v1.20.22) (2025-05-23)


### Bug Fixes

* Added new user join challenge push notif ([#246](https://github.com/FlyBodies/fly-fit/issues/246)) ([e6c4f6d](https://github.com/FlyBodies/fly-fit/commit/e6c4f6db4e7f256d36fad9c73cfa1454ae53781c))

## [1.20.21](https://github.com/FlyBodies/fly-fit/compare/v1.20.20...v1.20.21) (2025-05-22)


### Bug Fixes

* Eslint v9 upgrade and migration ([#245](https://github.com/FlyBodies/fly-fit/issues/245)) ([7a1c6f1](https://github.com/FlyBodies/fly-fit/commit/7a1c6f159fbb24802bd466242a7fd477230b7514))
* Fix web sign up bugs, invite code modal verbiage updates ([#241](https://github.com/FlyBodies/fly-fit/issues/241)) ([d2ad669](https://github.com/FlyBodies/fly-fit/commit/d2ad669d3d9d5137b7e06f3c9582c9422039c6eb))
* Knip fixes related to unused exports ([#242](https://github.com/FlyBodies/fly-fit/issues/242)) ([73af366](https://github.com/FlyBodies/fly-fit/commit/73af366701a22efac80ff41ced7fcbd49b9232be))
* Split apart welcome email and confirmation email ([#244](https://github.com/FlyBodies/fly-fit/issues/244)) ([0405cd2](https://github.com/FlyBodies/fly-fit/commit/0405cd28ecbe1c54804fe808ba88e82b58df5359))

## [1.20.20](https://github.com/FlyBodies/fly-fit/compare/v1.20.19...v1.20.20) (2025-05-17)


### Bug Fixes

* Added the sign up user form website ([#240](https://github.com/FlyBodies/fly-fit/issues/240)) ([4d695bd](https://github.com/FlyBodies/fly-fit/commit/4d695bdaa000bdf7bb28649ca86c4f5f3376f336))

## [1.20.19](https://github.com/FlyBodies/fly-fit/compare/v1.20.18...v1.20.19) (2025-05-09)


### Bug Fixes

* Added challenge public invite code support ([#239](https://github.com/FlyBodies/fly-fit/issues/239)) ([1b44d13](https://github.com/FlyBodies/fly-fit/commit/1b44d13c8e9e60da048c0790d0f6f0c2071220ce))
* Leave challenge & public invite codes ([#238](https://github.com/FlyBodies/fly-fit/issues/238)) ([cba9e05](https://github.com/FlyBodies/fly-fit/commit/cba9e0593cec37d8142c5244e8524569ac304b6e))

## [1.20.18](https://github.com/FlyBodies/fly-fit/compare/v1.20.17...v1.20.18) (2025-05-07)


### Bug Fixes

* Fix streak notifications being sent multiple times due to timezone ([17a263c](https://github.com/FlyBodies/fly-fit/commit/17a263c60765feb763e2f5c697fec387223ca428))
* Groups summary report, fix login issue ([#237](https://github.com/FlyBodies/fly-fit/issues/237)) ([18e1445](https://github.com/FlyBodies/fly-fit/commit/18e14456629e32ef87f625475e74f159ec46c640))

## [1.20.17](https://github.com/FlyBodies/fly-fit/compare/v1.20.16...v1.20.17) (2025-05-02)


### Bug Fixes

* Fix dynamic linking, push notification fixes, and other small updates ([#236](https://github.com/FlyBodies/fly-fit/issues/236)) ([79ac06e](https://github.com/FlyBodies/fly-fit/commit/79ac06effa1e41ea51d60072ebf8773acb17ea80))
* Fix memory limit, and adjust wellness blog logging ([86cc85e](https://github.com/FlyBodies/fly-fit/commit/86cc85e1cfb0cdb9227185e09a626d622d253e45))

## [1.20.16](https://github.com/FlyBodies/fly-fit/compare/v1.20.15...v1.20.16) (2025-04-30)


### Bug Fixes

* Redesigned and enhanced the fly-fit-link web project ([#234](https://github.com/FlyBodies/fly-fit/issues/234)) ([975bb3e](https://github.com/FlyBodies/fly-fit/commit/975bb3ed068c5d24fc433afa70e780d7e14fd1d3))
* Small UI updates and backend linking updates ([#235](https://github.com/FlyBodies/fly-fit/issues/235)) ([22e68a7](https://github.com/FlyBodies/fly-fit/commit/22e68a7b20148e76da6f1df67d9890a6f1716f0a))

## [1.20.15](https://github.com/FlyBodies/fly-fit/compare/v1.20.14...v1.20.15) (2025-04-28)


### Bug Fixes

* Fix health sync bug ([70b3d1a](https://github.com/FlyBodies/fly-fit/commit/70b3d1a4071b1555c22c9471584ef3ef8d7abf63))

## [1.20.14](https://github.com/FlyBodies/fly-fit/compare/v1.20.13...v1.20.14) (2025-04-26)


### Bug Fixes

* End challenge notifs, manual entry limit, manual entry trainer, banner image ([#232](https://github.com/FlyBodies/fly-fit/issues/232)) ([19dd9b6](https://github.com/FlyBodies/fly-fit/commit/19dd9b6f0380e5af8b12653af19649558ca1e3ba))
* MVP implementation of challenge invite code ([#233](https://github.com/FlyBodies/fly-fit/issues/233)) ([f36d1ce](https://github.com/FlyBodies/fly-fit/commit/f36d1ce7063f912374c724caad03f5666b5ad411))

## [1.20.13](https://github.com/FlyBodies/fly-fit/compare/v1.20.12...v1.20.13) (2025-04-19)


### Bug Fixes

* Fix pending challenge badge ([#231](https://github.com/FlyBodies/fly-fit/issues/231)) ([72f6abe](https://github.com/FlyBodies/fly-fit/commit/72f6abe7dde4c728168fc0f00af5b56df7ad0687))

## [1.20.12](https://github.com/FlyBodies/fly-fit/compare/v1.20.11...v1.20.12) (2025-04-18)


### Bug Fixes

* Added manual mileage entry ([#230](https://github.com/FlyBodies/fly-fit/issues/230)) ([ec01eac](https://github.com/FlyBodies/fly-fit/commit/ec01eac53445190addf9613b156cf59b8fd3405d))
* Added remaining groups challenge push notifications ([#229](https://github.com/FlyBodies/fly-fit/issues/229)) ([f0c1f1a](https://github.com/FlyBodies/fly-fit/commit/f0c1f1a2b4f816cd2e9fffbd9deecc10be51953c))

## [1.20.11](https://github.com/FlyBodies/fly-fit/compare/v1.20.10...v1.20.11) (2025-04-11)


### Bug Fixes

* Added Groups challenge feeds and posts ([#226](https://github.com/FlyBodies/fly-fit/issues/226)) ([a419b0a](https://github.com/FlyBodies/fly-fit/commit/a419b0a8a2761c7d5a6757d71c3fe7ce2f8fc379))
* Fix for 2 vulnerabilities ([#228](https://github.com/FlyBodies/fly-fit/issues/228)) ([d67746a](https://github.com/FlyBodies/fly-fit/commit/d67746a90c3400789dcaa37059cb0cfdf7c9ab59))
* Fix handling of creating/setting notification data for users ([319e7a5](https://github.com/FlyBodies/fly-fit/commit/319e7a5c97db947d067c039b9a186a36eeeb9d93))
* Health data document optimization ([0af0bf6](https://github.com/FlyBodies/fly-fit/commit/0af0bf696492083a0b7663a300eb2a2ed8fbd603))

## [1.20.10](https://github.com/FlyBodies/fly-fit/compare/v1.20.9...v1.20.10) (2025-04-07)


### Bug Fixes

* Fix app version code comparison ([aa854e5](https://github.com/FlyBodies/fly-fit/commit/aa854e5e38de64bd7ad4b4810915f884d0e75d96))
* Fix invoker role of task dispatch handlers ([39df2cc](https://github.com/FlyBodies/fly-fit/commit/39df2cc9bda3dfb0fab3542a79a4eea53429db0f))
* Health storage refactor fixes ([#225](https://github.com/FlyBodies/fly-fit/issues/225)) ([7c99556](https://github.com/FlyBodies/fly-fit/commit/7c99556627c8f98f3bc55e64f08fb39ebde0629b))

## [1.20.9](https://github.com/FlyBodies/fly-fit/compare/v1.20.8...v1.20.9) (2025-04-04)


### Bug Fixes

* Add warning log ([1cb1fc0](https://github.com/FlyBodies/fly-fit/commit/1cb1fc0242e838e56a2f8ce7793463f3b9084ae3))
* Fix movement streak calculation logic, fix create user bug ([#223](https://github.com/FlyBodies/fly-fit/issues/223)) ([30d4d01](https://github.com/FlyBodies/fly-fit/commit/30d4d016986afff81106c0191ab7d2782cfd6f73))
* Health storage refactor ([#224](https://github.com/FlyBodies/fly-fit/issues/224)) ([7b46878](https://github.com/FlyBodies/fly-fit/commit/7b4687881ab5ff2f5ede63d1ce7a5e427a3ecd78))

## [1.20.8](https://github.com/FlyBodies/fly-fit/compare/v1.20.7...v1.20.8) (2025-03-27)


### Bug Fixes

* Fix participant modal values ([b68d4a1](https://github.com/FlyBodies/fly-fit/commit/b68d4a19dea7284546436803a015875ebe007376))
* Import groups challenge endpoint ([#222](https://github.com/FlyBodies/fly-fit/issues/222)) ([2fabe47](https://github.com/FlyBodies/fly-fit/commit/2fabe4742d1789778f44be63a04249c16c9e0c88))

## [1.20.7](https://github.com/FlyBodies/fly-fit/compare/v1.20.6...v1.20.7) (2025-03-24)


### Bug Fixes

* Fix user search box not showing ([#221](https://github.com/FlyBodies/fly-fit/issues/221)) ([b702bda](https://github.com/FlyBodies/fly-fit/commit/b702bdab35c9a918b78e4c9963bbd16d998bc8b0))

## [1.20.6](https://github.com/FlyBodies/fly-fit/compare/v1.20.5...v1.20.6) (2025-03-21)


### Bug Fixes

* Added groups challenge UI ([#220](https://github.com/FlyBodies/fly-fit/issues/220)) ([b5e1c5f](https://github.com/FlyBodies/fly-fit/commit/b5e1c5f724228d90d2fea3ced6ea56080816dd4f))
* Fix iso date math relying on Date objects ([761d6ac](https://github.com/FlyBodies/fly-fit/commit/761d6ac2aa98b3cb9fb454f0d626a4ae337a59c1))

## [1.20.5](https://github.com/FlyBodies/fly-fit/compare/v1.20.4...v1.20.5) (2025-03-11)


### Bug Fixes

* Part 1 adding groups challenges, backend focus ([#219](https://github.com/FlyBodies/fly-fit/issues/219)) ([9e0c1ac](https://github.com/FlyBodies/fly-fit/commit/9e0c1acf9f154f80e3d84fe3cc02fd7219a1735f))

## [1.20.4](https://github.com/FlyBodies/fly-fit/compare/v1.20.3...v1.20.4) (2025-03-04)


### Bug Fixes

* Added All Teams feed on challenge page ([#218](https://github.com/FlyBodies/fly-fit/issues/218)) ([1637a51](https://github.com/FlyBodies/fly-fit/commit/1637a51cae2ce51eec0304518e0bf39e383f85cc))

## [1.20.3](https://github.com/FlyBodies/fly-fit/compare/v1.20.2...v1.20.3) (2025-02-26)


### Bug Fixes

* Small streak container fixes ([226567b](https://github.com/FlyBodies/fly-fit/commit/226567b425e17a7c5f1516864f12d208d38d6ce0))

## [1.20.2](https://github.com/FlyBodies/fly-fit/compare/v1.20.1...v1.20.2) (2025-02-25)


### Bug Fixes

* Fix the movement streak showing on day, week, month calendar views ([a7045d4](https://github.com/FlyBodies/fly-fit/commit/a7045d49bd6cabd9ba62a849958aaf883a79d5d1))

## [1.20.1](https://github.com/FlyBodies/fly-fit/compare/v1.20.0...v1.20.1) (2025-02-25)


### Bug Fixes

* Android UI fixes on different screen sizes ([#216](https://github.com/FlyBodies/fly-fit/issues/216)) ([5b678bd](https://github.com/FlyBodies/fly-fit/commit/5b678bd840c453422897ef3b2e592a7a41f5bf3f))
* Change movement streak to 1 day start ([#217](https://github.com/FlyBodies/fly-fit/issues/217)) ([0e58363](https://github.com/FlyBodies/fly-fit/commit/0e58363769adfa722690de1e94550f9470f4be4e))

# [1.20.0](https://github.com/FlyBodies/fly-fit/compare/v1.19.26...v1.20.0) (2025-02-22)


### Features

* Redesign V1 patches and bug fixes ([#215](https://github.com/FlyBodies/fly-fit/issues/215)) ([169f8ee](https://github.com/FlyBodies/fly-fit/commit/169f8ee83c616dce7d31ec62ed866cbdfa9a5471))

## [1.19.26](https://github.com/FlyBodies/fly-fit/compare/v1.19.25...v1.19.26) (2025-02-20)


### Bug Fixes

* Challenge popups redesign ([#214](https://github.com/FlyBodies/fly-fit/issues/214)) ([7fc3024](https://github.com/FlyBodies/fly-fit/commit/7fc302410926c236c89882a8f887d49a858c5225))

## [1.19.25](https://github.com/FlyBodies/fly-fit/compare/v1.19.24...v1.19.25) (2025-02-19)


### Bug Fixes

* Redesign challenge v1 ([#213](https://github.com/FlyBodies/fly-fit/issues/213)) ([949a814](https://github.com/FlyBodies/fly-fit/commit/949a81498f1fb8857e80262bb30c5973044b7e98))

## [1.19.24](https://github.com/FlyBodies/fly-fit/compare/v1.19.23...v1.19.24) (2025-02-14)


### Bug Fixes

* Redesign V1 feedback changes ([#212](https://github.com/FlyBodies/fly-fit/issues/212)) ([0e9646a](https://github.com/FlyBodies/fly-fit/commit/0e9646a568b4d8a032e628d63b88787cea754759))

## [1.19.23](https://github.com/FlyBodies/fly-fit/compare/v1.19.22...v1.19.23) (2025-02-14)


### Bug Fixes

* Home screen redesign v1 ([#211](https://github.com/FlyBodies/fly-fit/issues/211)) ([94824a7](https://github.com/FlyBodies/fly-fit/commit/94824a70afee7c0e02087b749ccfb04bc6e90327))

## [1.19.22](https://github.com/FlyBodies/fly-fit/compare/v1.19.21...v1.19.22) (2025-02-06)


### Bug Fixes

* Added "Update" button to FlyFit banner ([c0dea5e](https://github.com/FlyBodies/fly-fit/commit/c0dea5ea92b7c95dbb3375ea67c1dd48f6609ee4))
* Fix the update key on the update banner ([d6281ab](https://github.com/FlyBodies/fly-fit/commit/d6281abf937a9e965ef3c31efde6f11f41e95104))

## [1.19.21](https://github.com/FlyBodies/fly-fit/compare/v1.19.20...v1.19.21) (2025-02-06)


### Bug Fixes

* Add update banner to home screen ([#210](https://github.com/FlyBodies/fly-fit/issues/210)) ([3118261](https://github.com/FlyBodies/fly-fit/commit/3118261bcb52d5f3c638ba5c8bc45917f40d5d44))

## [1.19.20](https://github.com/FlyBodies/fly-fit/compare/v1.19.19...v1.19.20) (2025-02-04)


### Bug Fixes

* Added trainer messaging for challenges, fix challenge stage time bugs ([#209](https://github.com/FlyBodies/fly-fit/issues/209)) ([e9c3f4b](https://github.com/FlyBodies/fly-fit/commit/e9c3f4b089d1dc8f5af5fd4ea57d767a6862cd8e))

## [1.19.19](https://github.com/FlyBodies/fly-fit/compare/v1.19.18...v1.19.19) (2025-01-30)


### Bug Fixes

* Android remove data origin filtering with health connect, add react compiler ([#208](https://github.com/FlyBodies/fly-fit/issues/208)) ([915ae23](https://github.com/FlyBodies/fly-fit/commit/915ae23a576625251e634e83becb2c607223dbf1))

## [1.19.18](https://github.com/FlyBodies/fly-fit/compare/v1.19.17...v1.19.18) (2025-01-29)


### Bug Fixes

* Fix Health Connect sync issues with data origins ([#207](https://github.com/FlyBodies/fly-fit/issues/207)) ([1045958](https://github.com/FlyBodies/fly-fit/commit/104595884bdaf17a4f9df2fa2ec97cf099c6f108))

## [1.19.17](https://github.com/FlyBodies/fly-fit/compare/v1.19.16...v1.19.17) (2025-01-25)


### Bug Fixes

* Revert max teams list size to 5 ([fadbdbd](https://github.com/FlyBodies/fly-fit/commit/fadbdbd75f6fe77d2b175a3defb7e8174e243159))

## [1.19.16](https://github.com/FlyBodies/fly-fit/compare/v1.19.15...v1.19.16) (2025-01-24)


### Bug Fixes

* Revert rn version, small bug fixes ([#206](https://github.com/FlyBodies/fly-fit/issues/206)) ([f130348](https://github.com/FlyBodies/fly-fit/commit/f13034800320d261847fa38e3d2296728dc23c66))

## [1.19.15](https://github.com/FlyBodies/fly-fit/compare/v1.19.14...v1.19.15) (2025-01-24)


### Bug Fixes

* Fix the tracking device modal showing up, upgrade RN 0.77 ([cd19f78](https://github.com/FlyBodies/fly-fit/commit/cd19f784185f5eb098ffe543fd61421ea6e674e7))

## [1.19.14](https://github.com/FlyBodies/fly-fit/compare/v1.19.13...v1.19.14) (2025-01-23)


### Bug Fixes

* Navigation fixes and challenge data export UI update ([#205](https://github.com/FlyBodies/fly-fit/issues/205)) ([7ffbffb](https://github.com/FlyBodies/fly-fit/commit/7ffbffb515774a2880cce5a4d8656d2cf413d9f8))

## [1.19.13](https://github.com/FlyBodies/fly-fit/compare/v1.19.12...v1.19.13) (2025-01-22)


### Bug Fixes

* Implement goal prompting ([#204](https://github.com/FlyBodies/fly-fit/issues/204)) ([81be1b1](https://github.com/FlyBodies/fly-fit/commit/81be1b14a9e2df002556b41659eff2344b71c179))

## [1.19.12](https://github.com/FlyBodies/fly-fit/compare/v1.19.11...v1.19.12) (2025-01-17)


### Bug Fixes

* Fix Android login issue ([#203](https://github.com/FlyBodies/fly-fit/issues/203)) ([8bb77f9](https://github.com/FlyBodies/fly-fit/commit/8bb77f933b6c1859d021f59470f9475db6b1e469))

## [1.19.11](https://github.com/FlyBodies/fly-fit/compare/v1.19.10...v1.19.11) (2025-01-15)


### Bug Fixes

* Fix Android weight sync issue ([0e2596f](https://github.com/FlyBodies/fly-fit/commit/0e2596fff3dbb1ebcb0b23f7f28c8dac2ca7b424))
* Small bug fixes, initial goal modal ([#202](https://github.com/FlyBodies/fly-fit/issues/202)) ([4b25f00](https://github.com/FlyBodies/fly-fit/commit/4b25f00b0c0017867327336ef41f5f1cc4b94676))

## [1.19.10](https://github.com/FlyBodies/fly-fit/compare/v1.19.9...v1.19.10) (2025-01-15)


### Bug Fixes

* Fix Android login bug ([69cda67](https://github.com/FlyBodies/fly-fit/commit/69cda6782bb3b14324018ddb0a95a806b5eef5d7))

## [1.19.9](https://github.com/FlyBodies/fly-fit/compare/v1.19.8...v1.19.9) (2025-01-10)


### Bug Fixes

* Streak contianer verbiage fixes, implemented streak v2 notifications ([#201](https://github.com/FlyBodies/fly-fit/issues/201)) ([a50c4d1](https://github.com/FlyBodies/fly-fit/commit/a50c4d1b65599b36e904f79c083b2abd02ab935f))

## [1.19.8](https://github.com/FlyBodies/fly-fit/compare/v1.19.7...v1.19.8) (2025-01-08)


### Bug Fixes

* Fix bug where notifications being disabled in race condition ([f93866d](https://github.com/FlyBodies/fly-fit/commit/f93866d93b2f4627e069761621a168d485eba5c4))

## [1.19.7](https://github.com/FlyBodies/fly-fit/compare/v1.19.6...v1.19.7) (2025-01-08)


### Bug Fixes

* Added configurations for streak push notifications, and other notification rework ([#200](https://github.com/FlyBodies/fly-fit/issues/200)) ([c8b8d61](https://github.com/FlyBodies/fly-fit/commit/c8b8d617e75b0ad1a077a8d50acb8078eea004e5))
* Fix small display issues for challenge steps display, and challenge leaderboard ([6b309ad](https://github.com/FlyBodies/fly-fit/commit/6b309ad78c241ef86457330b3c4066c91bfe0388))

## [1.19.6](https://github.com/FlyBodies/fly-fit/compare/v1.19.5...v1.19.6) (2025-01-03)


### Bug Fixes

* Streak fixes, adding movement streak push notif ([#199](https://github.com/FlyBodies/fly-fit/issues/199)) ([b80360a](https://github.com/FlyBodies/fly-fit/commit/b80360a61f7f96bf8b35a74399b5419ef40206c7))

## [1.19.5](https://github.com/FlyBodies/fly-fit/compare/v1.19.4...v1.19.5) (2025-01-02)


### Bug Fixes

* Small movement streak adjustments, and other backend fixes ([#198](https://github.com/FlyBodies/fly-fit/issues/198)) ([a73e2cb](https://github.com/FlyBodies/fly-fit/commit/a73e2cbae710684161e91d58bdae7ac48971612b))

## [1.19.4](https://github.com/FlyBodies/fly-fit/compare/v1.19.3...v1.19.4) (2025-01-02)


### Bug Fixes

* Fix date inconsistency issues ([b6e795a](https://github.com/FlyBodies/fly-fit/commit/b6e795a62d3d36bbd55b89a056ccbb60463d0699))
* Fix quiz progress, movement streak, and other time/date fixes ([#197](https://github.com/FlyBodies/fly-fit/issues/197)) ([07b9c16](https://github.com/FlyBodies/fly-fit/commit/07b9c166e8ca3299adecf9790084b31357cd5484))

## [1.19.3](https://github.com/FlyBodies/fly-fit/compare/v1.19.2...v1.19.3) (2024-12-27)


### Bug Fixes

* Streak fixes, upload fix ([#196](https://github.com/FlyBodies/fly-fit/issues/196)) ([0b9052a](https://github.com/FlyBodies/fly-fit/commit/0b9052a0222dc5110afae8d150fb9c25583d672f))

## [1.19.2](https://github.com/FlyBodies/fly-fit/compare/v1.19.1...v1.19.2) (2024-12-26)


### Bug Fixes

* Fix clientSummaryDto type errors ([8b81cf9](https://github.com/FlyBodies/fly-fit/commit/8b81cf96e7ad4ba3eac30e4d161fc7eb80f7cfcf))
* Refactor and migrate movement streak to new collection ([#195](https://github.com/FlyBodies/fly-fit/issues/195)) ([20e9660](https://github.com/FlyBodies/fly-fit/commit/20e9660e38fc47786ef9b1c509db63d0eeec7c03))

## [1.19.1](https://github.com/FlyBodies/fly-fit/compare/v1.19.0...v1.19.1) (2024-12-22)


### Bug Fixes

* Fix the label style on calendar to only be for Android ([133b740](https://github.com/FlyBodies/fly-fit/commit/133b740c128b00af1b427fe4fd987f89872fdf3c))

# [1.19.0](https://github.com/FlyBodies/fly-fit/compare/v1.18.0...v1.19.0) (2024-12-21)


### Features

* Expo 52 upgrade, and other small UI fixes ([#194](https://github.com/FlyBodies/fly-fit/issues/194)) ([1ea3ac8](https://github.com/FlyBodies/fly-fit/commit/1ea3ac88ddf8f0097866f1d3ae45d91be14a845d))

# [1.18.0](https://github.com/FlyBodies/fly-fit/compare/v1.17.3...v1.18.0) (2024-12-19)


### Features

* Health connect upgrade and date fixes ([#193](https://github.com/FlyBodies/fly-fit/issues/193)) ([ee7c92a](https://github.com/FlyBodies/fly-fit/commit/ee7c92a7f81cc74e747c33dfdd3f1f44c29ff59f))

## [1.17.3](https://github.com/FlyBodies/fly-fit/compare/v1.17.2...v1.17.3) (2024-12-18)


### Bug Fixes

* Fix timezone bugs ([#192](https://github.com/FlyBodies/fly-fit/issues/192)) ([6b736e2](https://github.com/FlyBodies/fly-fit/commit/6b736e22aa6e51bff73cfa57ea006d8aa249e451))

## [1.17.2](https://github.com/FlyBodies/fly-fit/compare/v1.17.1...v1.17.2) (2024-12-14)


### Bug Fixes

* Fix auth login and started to implement challenge timezone ([#191](https://github.com/FlyBodies/fly-fit/issues/191)) ([8d2d1c6](https://github.com/FlyBodies/fly-fit/commit/8d2d1c69ce70fd7aac6e4852ee56e601389ad2fe))

## [1.17.1](https://github.com/FlyBodies/fly-fit/compare/v1.17.0...v1.17.1) (2024-12-12)


### Bug Fixes

* Fix phone auth bug not logging users in ([f261691](https://github.com/FlyBodies/fly-fit/commit/f261691c9abf5c0883de013758e8c9ad96808177))

# [1.17.0](https://github.com/FlyBodies/fly-fit/compare/v1.16.2...v1.17.0) (2024-12-12)


### Features

* Add phone code sign in ([#190](https://github.com/FlyBodies/fly-fit/issues/190)) ([ec5517d](https://github.com/FlyBodies/fly-fit/commit/ec5517d8743268aa3f720a7a3329518a02f64ad9))

## [1.16.2](https://github.com/FlyBodies/fly-fit/compare/v1.16.1...v1.16.2) (2024-12-07)


### Bug Fixes

* Stage fixes for totals and ranking ([#189](https://github.com/FlyBodies/fly-fit/issues/189)) ([195661b](https://github.com/FlyBodies/fly-fit/commit/195661b581025e009d0403260c2a869030fe462c))

## [1.16.1](https://github.com/FlyBodies/fly-fit/compare/v1.16.0...v1.16.1) (2024-12-06)


### Bug Fixes

* Implemented stage ranking for individuals, fixed mileage display ([#188](https://github.com/FlyBodies/fly-fit/issues/188)) ([64a7f82](https://github.com/FlyBodies/fly-fit/commit/64a7f82811c0ea7a80952a1003be2790989f26a2))

# [1.16.0](https://github.com/FlyBodies/fly-fit/compare/v1.15.7...v1.16.0) (2024-12-06)


### Features

* Added challenge stages ([#187](https://github.com/FlyBodies/fly-fit/issues/187)) ([ef218ef](https://github.com/FlyBodies/fly-fit/commit/ef218ef2c8a40da86080d45e788aac211e51722c))

## [1.15.7](https://github.com/FlyBodies/fly-fit/compare/v1.15.6...v1.15.7) (2024-11-30)


### Bug Fixes

* Wellness quiz streak fixes, android health sync fixes ([#186](https://github.com/FlyBodies/fly-fit/issues/186)) ([d71b55f](https://github.com/FlyBodies/fly-fit/commit/d71b55fb37029222fa919f6c59edd04e1d6f0ef7))

## [1.15.6](https://github.com/FlyBodies/fly-fit/compare/v1.15.5...v1.15.6) (2024-11-27)


### Bug Fixes

* Small improvements to linking page, ([53a0178](https://github.com/FlyBodies/fly-fit/commit/53a0178da9a658771c3dc3063c4cd5cb8cec42f4))
* Timezone fixes, tracking device updates ([#185](https://github.com/FlyBodies/fly-fit/issues/185)) ([dbd5348](https://github.com/FlyBodies/fly-fit/commit/dbd5348a281fae6ff0915fcdb74d8c0f00e2261b))

## [1.15.5](https://github.com/FlyBodies/fly-fit/compare/v1.15.4...v1.15.5) (2024-11-23)


### Bug Fixes

* Health sync fixes, and share workout ([#184](https://github.com/FlyBodies/fly-fit/issues/184)) ([6ef475a](https://github.com/FlyBodies/fly-fit/commit/6ef475a68d2d861f92f9289bb0325e23494de921))

## [1.15.4](https://github.com/FlyBodies/fly-fit/compare/v1.15.3...v1.15.4) (2024-11-21)


### Bug Fixes

* Added Fitbit/Garmin emails, added new login authentication ([#183](https://github.com/FlyBodies/fly-fit/issues/183)) ([a14f50a](https://github.com/FlyBodies/fly-fit/commit/a14f50aba16109911cefa431f5628675fe156542))

## [1.15.3](https://github.com/FlyBodies/fly-fit/compare/v1.15.2...v1.15.3) (2024-11-15)


### Bug Fixes

* Fix the participant challenge card showing total value ([#182](https://github.com/FlyBodies/fly-fit/issues/182)) ([f495b59](https://github.com/FlyBodies/fly-fit/commit/f495b590ab33143a82e51579208c57d599979c7e))

## [1.15.2](https://github.com/FlyBodies/fly-fit/compare/v1.15.1...v1.15.2) (2024-11-15)


### Bug Fixes

* Fix steps challenge bugs ([#181](https://github.com/FlyBodies/fly-fit/issues/181)) ([fa1151e](https://github.com/FlyBodies/fly-fit/commit/fa1151e00bea5755dc65ebe10ac10e006b5138d4))

## [1.15.1](https://github.com/FlyBodies/fly-fit/compare/v1.15.0...v1.15.1) (2024-11-15)


### Bug Fixes

* Implemented meal types, meal completion, duplication, and trainer meal creation ([#180](https://github.com/FlyBodies/fly-fit/issues/180)) ([726cf86](https://github.com/FlyBodies/fly-fit/commit/726cf8604dc2933dac7b9763ff4517046affb831))

# [1.15.0](https://github.com/FlyBodies/fly-fit/compare/v1.14.14...v1.15.0) (2024-11-13)


### Bug Fixes

* Added store rating feature ([b22ff42](https://github.com/FlyBodies/fly-fit/commit/b22ff42e2cb7a608f92d14e38099a3f0ff38e5eb))
* Feature flagged app store rating ([54a9329](https://github.com/FlyBodies/fly-fit/commit/54a93290f36af4e9d322d8f3420fb47aa96c3618))
* Fix alignment of modals on ipad, added new screenshots ([c8b6d29](https://github.com/FlyBodies/fly-fit/commit/c8b6d296e99165868f4f14f062d1fa2301255058))
* Fix clientSummary workout summation ([bd45970](https://github.com/FlyBodies/fly-fit/commit/bd45970a3ba8b1c05b927cb54cf1cd3337bbd03d))


### Features

* Added meal creation, fix admin create user bug ([#179](https://github.com/FlyBodies/fly-fit/issues/179)) ([301d2f9](https://github.com/FlyBodies/fly-fit/commit/301d2f9979d8d31fbcbc37ba743058924d520a3b))

## [1.14.14](https://github.com/FlyBodies/fly-fit/compare/v1.14.13...v1.14.14) (2024-11-01)


### Bug Fixes

* Fix EditUser requiring first/last name, fix daily data updating of challenges ([#178](https://github.com/FlyBodies/fly-fit/issues/178)) ([f62f0ec](https://github.com/FlyBodies/fly-fit/commit/f62f0ecb0331c1be2b555fd21b2383dc9e0eb48c))

## [1.14.13](https://github.com/FlyBodies/fly-fit/compare/v1.14.12...v1.14.13) (2024-10-31)


### Bug Fixes

* Fitbit sync fixes, Oct 31st fixes ([#177](https://github.com/FlyBodies/fly-fit/issues/177)) ([9efbe19](https://github.com/FlyBodies/fly-fit/commit/9efbe19dea4c8e395650fae745f3e31863aa9c5b))
* Fix sorting bug in test cases ([8c6b3e4](https://github.com/FlyBodies/fly-fit/commit/8c6b3e42a9c47098158f8bf86753b4c9e69c8550))

## [1.14.12](https://github.com/FlyBodies/fly-fit/compare/v1.14.11...v1.14.12) (2024-10-31)


### Bug Fixes

* Fix onContinue functionality of Apple Watch modal pop up ([9840ed5](https://github.com/FlyBodies/fly-fit/commit/9840ed5f5e95753360074654d2a39188065879bd))
* Revert back change to on apple open modal ([d953388](https://github.com/FlyBodies/fly-fit/commit/d9533886dca943c7db8cb8e00defdad399061e01))

## [1.14.11](https://github.com/FlyBodies/fly-fit/compare/v1.14.10...v1.14.11) (2024-10-30)


### Bug Fixes

* Add Apple Watch connection email ([#176](https://github.com/FlyBodies/fly-fit/issues/176)) ([dea1cbb](https://github.com/FlyBodies/fly-fit/commit/dea1cbb13e4019341842f9cd1f41ef702bd63294))

## [1.14.10](https://github.com/FlyBodies/fly-fit/compare/v1.14.9...v1.14.10) (2024-10-24)


### Bug Fixes

* Fitbit fixes, login fix, backend transactional update fixes ([#175](https://github.com/FlyBodies/fly-fit/issues/175)) ([10e090c](https://github.com/FlyBodies/fly-fit/commit/10e090c5c33603ac30562bb52b761e087fb86845))
* Implemented queueing for summary report generation and email sending ([#174](https://github.com/FlyBodies/fly-fit/issues/174)) ([9b8441e](https://github.com/FlyBodies/fly-fit/commit/9b8441e0f4ca1022f84e77376c3b276c8fcef233))

## [1.14.9](https://github.com/FlyBodies/fly-fit/compare/v1.14.8...v1.14.9) (2024-10-18)


### Bug Fixes

* Fix bug where Fitbit distance double counted ([#173](https://github.com/FlyBodies/fly-fit/issues/173)) ([404dc51](https://github.com/FlyBodies/fly-fit/commit/404dc51909cd9b09d6f2cad24750699942b426b4))

## [1.14.8](https://github.com/FlyBodies/fly-fit/compare/v1.14.7...v1.14.8) (2024-10-17)


### Bug Fixes

* Fitbit fix mileage claculation ([#172](https://github.com/FlyBodies/fly-fit/issues/172)) ([409f9f9](https://github.com/FlyBodies/fly-fit/commit/409f9f983562844fca4033fffc5f9f439560fd62))

## [1.14.7](https://github.com/FlyBodies/fly-fit/compare/v1.14.6...v1.14.7) (2024-10-16)


### Bug Fixes

* Fitbit fixes ([#171](https://github.com/FlyBodies/fly-fit/issues/171)) ([78446ef](https://github.com/FlyBodies/fly-fit/commit/78446ef8d1c56bf065e227f71d93c64ddeb902d7))

## [1.14.6](https://github.com/FlyBodies/fly-fit/compare/v1.14.5...v1.14.6) (2024-10-15)


### Bug Fixes

* Fix caching sync issue and other android fixes ([#170](https://github.com/FlyBodies/fly-fit/issues/170)) ([f8a72e1](https://github.com/FlyBodies/fly-fit/commit/f8a72e1de841f680bf2c77b2827ed773ad4c729e))

## [1.14.5](https://github.com/FlyBodies/fly-fit/compare/v1.14.4...v1.14.5) (2024-10-12)


### Bug Fixes

* Android health sync settings fixes and other small feedback ([#169](https://github.com/FlyBodies/fly-fit/issues/169)) ([325c25b](https://github.com/FlyBodies/fly-fit/commit/325c25b8a5dedbd153c33fbf992e2827eacf5907))

## [1.14.4](https://github.com/FlyBodies/fly-fit/compare/v1.14.3...v1.14.4) (2024-10-11)


### Bug Fixes

* Feedback and challenge notifications fixes ([#168](https://github.com/FlyBodies/fly-fit/issues/168)) ([4d81c3c](https://github.com/FlyBodies/fly-fit/commit/4d81c3cad366406d6a29b0640152f0488f0d477a))

## [1.14.3](https://github.com/FlyBodies/fly-fit/compare/v1.14.2...v1.14.3) (2024-10-10)


### Bug Fixes

* Fitbit fixes, challenge notification updates ([#167](https://github.com/FlyBodies/fly-fit/issues/167)) ([af2b17f](https://github.com/FlyBodies/fly-fit/commit/af2b17f2a52b9cf44fefcdc9cc50de33c05df2ce))

## [1.14.2](https://github.com/FlyBodies/fly-fit/compare/v1.14.1...v1.14.2) (2024-10-05)


### Bug Fixes

* Challenge feed small tweaks and performance bug fix ([#166](https://github.com/FlyBodies/fly-fit/issues/166)) ([5f7b2d5](https://github.com/FlyBodies/fly-fit/commit/5f7b2d5f4157124ef93c8063f9258d1be230ed62))

## [1.14.1](https://github.com/FlyBodies/fly-fit/compare/v1.14.0...v1.14.1) (2024-10-04)


### Bug Fixes

* Feedback fixes for wearables and health sync changes ([#165](https://github.com/FlyBodies/fly-fit/issues/165)) ([6d5b417](https://github.com/FlyBodies/fly-fit/commit/6d5b41780556b548013bde0e4acafeb71242bc61))

# [1.14.0](https://github.com/FlyBodies/fly-fit/compare/v1.13.8...v1.14.0) (2024-10-02)


### Bug Fixes

* Added wearable pop-up and Fitbit integration ([#163](https://github.com/FlyBodies/fly-fit/issues/163)) ([c4d2f82](https://github.com/FlyBodies/fly-fit/commit/c4d2f824e3b05c921a0672c6f5fd72cbf99c811e))


### Features

* Added challenge feed image upload ([#164](https://github.com/FlyBodies/fly-fit/issues/164)) ([bb6474a](https://github.com/FlyBodies/fly-fit/commit/bb6474a5ff98058a7d2ed617399f8f04890d7f1f))

## [1.13.8](https://github.com/FlyBodies/fly-fit/compare/v1.13.7...v1.13.8) (2024-10-02)


### Bug Fixes

* Fixed clearing out all of local storage on logout ([d60cc5f](https://github.com/FlyBodies/fly-fit/commit/d60cc5f79127d0f3a8d9498d43ef24f6300a004c))

## [1.13.7](https://github.com/FlyBodies/fly-fit/compare/v1.13.6...v1.13.7) (2024-09-27)


### Bug Fixes

* Improved loading spinner on wellness quiz ([ae593fa](https://github.com/FlyBodies/fly-fit/commit/ae593fab26e6e36aa4bbc1234fa76b90c2c71043))

## [1.13.6](https://github.com/FlyBodies/fly-fit/compare/v1.13.5...v1.13.6) (2024-09-27)


### Bug Fixes

* Enabled teams challenge summary reports to be created ([#160](https://github.com/FlyBodies/fly-fit/issues/160)) ([88091d8](https://github.com/FlyBodies/fly-fit/commit/88091d83ce4b73c6cccd0bd2359672c6a6a9d572))

## [1.13.5](https://github.com/FlyBodies/fly-fit/compare/v1.13.4...v1.13.5) (2024-09-27)


### Bug Fixes

* Fixed wellness quiz streak to allow for yesterday start of streak ([#159](https://github.com/FlyBodies/fly-fit/issues/159)) ([bc44876](https://github.com/FlyBodies/fly-fit/commit/bc448767ead325a8ef559a3f409efcac5a38678c))

## [1.13.4](https://github.com/FlyBodies/fly-fit/compare/v1.13.3...v1.13.4) (2024-09-27)


### Bug Fixes

* Added daily wellness quiz review, streak, and counts ([#158](https://github.com/FlyBodies/fly-fit/issues/158)) ([34ee517](https://github.com/FlyBodies/fly-fit/commit/34ee5174f667586c9346d93981223cdf0b93dad8))

## [1.13.3](https://github.com/FlyBodies/fly-fit/compare/v1.13.2...v1.13.3) (2024-09-18)


### Bug Fixes

* Added goal toggle, wellness education ordering fixes ([#157](https://github.com/FlyBodies/fly-fit/issues/157)) ([0017a5c](https://github.com/FlyBodies/fly-fit/commit/0017a5cc8777a93b912fab005f24ae58df71c9b0))

## [1.13.2](https://github.com/FlyBodies/fly-fit/compare/v1.13.1...v1.13.2) (2024-09-17)


### Bug Fixes

* Fixed wellness quiz notifications, wellness blog name refactor, clean functions/utils folder ([#156](https://github.com/FlyBodies/fly-fit/issues/156)) ([04b1ba2](https://github.com/FlyBodies/fly-fit/commit/04b1ba2ab00998b48b64518481616a231610591f))

## [1.13.1](https://github.com/FlyBodies/fly-fit/compare/v1.13.0...v1.13.1) (2024-09-13)


### Bug Fixes

* Notification updates, add FAQ, Nan bug, challenge accept, distance setting verbiage change ([#155](https://github.com/FlyBodies/fly-fit/issues/155)) ([cabda14](https://github.com/FlyBodies/fly-fit/commit/cabda14920a790c103edd2954d9eccac96030515))

# [1.13.0](https://github.com/FlyBodies/fly-fit/compare/v1.12.8...v1.13.0) (2024-09-12)


### Bug Fixes

* Added daily quiz notifications ([#154](https://github.com/FlyBodies/fly-fit/issues/154)) ([b1f4d94](https://github.com/FlyBodies/fly-fit/commit/b1f4d94d33876e0c4945514348f0576eef346e9b))
* Added daily wellness quotes ([#151](https://github.com/FlyBodies/fly-fit/issues/151)) ([6cef0cc](https://github.com/FlyBodies/fly-fit/commit/6cef0cc19a27e435ab0a388e4e8714252e255fb1))
* Added new challenge notifications ([#153](https://github.com/FlyBodies/fly-fit/issues/153)) ([bacf74b](https://github.com/FlyBodies/fly-fit/commit/bacf74b9af142d2c92aac21d5949d0302b7e3ce8))
* Make all challenges notifications compatible with all challenge variants ([#152](https://github.com/FlyBodies/fly-fit/issues/152)) ([4261642](https://github.com/FlyBodies/fly-fit/commit/4261642c1052be546c8168332d12610c928915f0))


### Features

* Added daily wellness quiz ([#150](https://github.com/FlyBodies/fly-fit/issues/150)) ([d31efd7](https://github.com/FlyBodies/fly-fit/commit/d31efd70f82cbe413b096796d6e81068db41d325))

## [1.12.8](https://github.com/FlyBodies/fly-fit/compare/v1.12.7...v1.12.8) (2024-09-06)


### Bug Fixes

* Team dropdown cleanups and fixes ([#149](https://github.com/FlyBodies/fly-fit/issues/149)) ([5b0b8f5](https://github.com/FlyBodies/fly-fit/commit/5b0b8f5eed8de52a9ffcd7cb808e9151e353f217))

## [1.12.7](https://github.com/FlyBodies/fly-fit/compare/v1.12.6...v1.12.7) (2024-09-05)


### Bug Fixes

* Added import user script and endpoint ([#148](https://github.com/FlyBodies/fly-fit/issues/148)) ([a10eb5d](https://github.com/FlyBodies/fly-fit/commit/a10eb5dfab2a13eb820773571e99861e82b2f84e))
* Added Team dropdown on Team tab for trainers ([#147](https://github.com/FlyBodies/fly-fit/issues/147)) ([ce5ed13](https://github.com/FlyBodies/fly-fit/commit/ce5ed134dca2762982cbe6bb451c7f3defa9948d))

## [1.12.6](https://github.com/FlyBodies/fly-fit/compare/v1.12.5...v1.12.6) (2024-09-04)


### Bug Fixes

* Fix challenge participant memoization bug ([e2c6099](https://github.com/FlyBodies/fly-fit/commit/e2c6099fafdcb1ed713fd00fc31845ce721ae122))

## [1.12.5](https://github.com/FlyBodies/fly-fit/compare/v1.12.4...v1.12.5) (2024-09-03)


### Bug Fixes

* Fix challenge force update, challenge sync issues ([#146](https://github.com/FlyBodies/fly-fit/issues/146)) ([4927f32](https://github.com/FlyBodies/fly-fit/commit/4927f323cc7a2aa018ceaed03ef387093dd0d8e6))

## [1.12.4](https://github.com/FlyBodies/fly-fit/compare/v1.12.3...v1.12.4) (2024-08-30)


### Bug Fixes

* Fixed future dated teams challenges not rendering leaderboard ([0aeceef](https://github.com/FlyBodies/fly-fit/commit/0aeceef9a02de1ef531684f66ac00e4a9e82a7c9))

## [1.12.3](https://github.com/FlyBodies/fly-fit/compare/v1.12.2...v1.12.3) (2024-08-30)


### Bug Fixes

* Added team challenge feed ([#145](https://github.com/FlyBodies/fly-fit/issues/145)) ([e501473](https://github.com/FlyBodies/fly-fit/commit/e50147357d9395e7f434be61239a12f5bcfc6ba2))

## [1.12.2](https://github.com/FlyBodies/fly-fit/compare/v1.12.1...v1.12.2) (2024-08-29)


### Bug Fixes

* Teams challenge updates and patches ([#144](https://github.com/FlyBodies/fly-fit/issues/144)) ([d434cfe](https://github.com/FlyBodies/fly-fit/commit/d434cfe4006fe34693c64d20b7c9e39c38273ff4))

## [1.12.1](https://github.com/FlyBodies/fly-fit/compare/v1.12.0...v1.12.1) (2024-08-29)


### Bug Fixes

* Fixed passing props to tab view components ([2b1cc2a](https://github.com/FlyBodies/fly-fit/commit/2b1cc2a914a889a297ca5efbcb77a1a1fc1c3691))

# [1.12.0](https://github.com/FlyBodies/fly-fit/compare/v1.11.18...v1.12.0) (2024-08-28)


### Features

* Added Teams challenge ([#143](https://github.com/FlyBodies/fly-fit/issues/143)) ([1022fb2](https://github.com/FlyBodies/fly-fit/commit/1022fb252649a41f16caade44f220f3e00b75fa5))

## [1.11.18](https://github.com/FlyBodies/fly-fit/compare/v1.11.17...v1.11.18) (2024-08-24)


### Bug Fixes

* Fixed current date/today calculation ([8e8938a](https://github.com/FlyBodies/fly-fit/commit/8e8938a7653740513b1aa2653580f9a4220bcbf9))

## [1.11.17](https://github.com/FlyBodies/fly-fit/compare/v1.11.16...v1.11.17) (2024-08-23)


### Bug Fixes

* Calendar feedback and enhancements ([#142](https://github.com/FlyBodies/fly-fit/issues/142)) ([9263c88](https://github.com/FlyBodies/fly-fit/commit/9263c889d6696dd50fe975ec94e16ffd74143601))

## [1.11.16](https://github.com/FlyBodies/fly-fit/compare/v1.11.15...v1.11.16) (2024-08-22)


### Bug Fixes

* Calendar feedback adjustments ([#141](https://github.com/FlyBodies/fly-fit/issues/141)) ([4a40efd](https://github.com/FlyBodies/fly-fit/commit/4a40efda5a58e52c1a8671104622aee9228e18f3))

## [1.11.15](https://github.com/FlyBodies/fly-fit/compare/v1.11.14...v1.11.15) (2024-08-22)


### Bug Fixes

* Implemented calendar feature redesign ([#140](https://github.com/FlyBodies/fly-fit/issues/140)) ([57a774f](https://github.com/FlyBodies/fly-fit/commit/57a774f93be6183ad6698bded70990bd1f227624))

## [1.11.14](https://github.com/FlyBodies/fly-fit/compare/v1.11.13...v1.11.14) (2024-08-19)


### Bug Fixes

* Enhance user search with Algolia ([#138](https://github.com/FlyBodies/fly-fit/issues/138)) ([8cb6091](https://github.com/FlyBodies/fly-fit/commit/8cb6091a7b949c9caaba97223d2783158c0b4daf))

## [1.11.13](https://github.com/FlyBodies/fly-fit/compare/v1.11.12...v1.11.13) (2024-08-16)


### Bug Fixes

* Implement user account deletion ([#137](https://github.com/FlyBodies/fly-fit/issues/137)) ([bfa1172](https://github.com/FlyBodies/fly-fit/commit/bfa1172f21bafa1706947f3c600eb324577faf88))

## [1.11.12](https://github.com/FlyBodies/fly-fit/compare/v1.11.11...v1.11.12) (2024-08-16)


### Bug Fixes

* Added sign up user screen with backend changes ([#132](https://github.com/FlyBodies/fly-fit/issues/132)) ([3c2bad8](https://github.com/FlyBodies/fly-fit/commit/3c2bad883c7fc0ac536de5efa4743a716b9c4d53))
* Added the blog post image to home page ([#134](https://github.com/FlyBodies/fly-fit/issues/134)) ([70d165b](https://github.com/FlyBodies/fly-fit/commit/70d165b4f81854334eb57abdad4161011e098b83))
* Data migration for workouts and challenges to use IDs ([#130](https://github.com/FlyBodies/fly-fit/issues/130)) ([3aaa350](https://github.com/FlyBodies/fly-fit/commit/3aaa350f787807a93e4e763f588bc261a95f2e17))
* Trainer receives notification when client complete workout ([#133](https://github.com/FlyBodies/fly-fit/issues/133)) ([cd0cf94](https://github.com/FlyBodies/fly-fit/commit/cd0cf94abce44b9994cb7b2ac91136b1eaf098e2))
* Updated welcome email with small adjustments ([#135](https://github.com/FlyBodies/fly-fit/issues/135)) ([f8b0087](https://github.com/FlyBodies/fly-fit/commit/f8b008721df2cb94a2003971af420d529502db01))

## [1.11.11](https://github.com/FlyBodies/fly-fit/compare/v1.11.10...v1.11.11) (2024-08-14)


### Bug Fixes

* Phase 1 of EAS Update migration ([#131](https://github.com/FlyBodies/fly-fit/issues/131)) ([b3235d9](https://github.com/FlyBodies/fly-fit/commit/b3235d98d9170a4de769a4315cf9fbb60b02854b))

## [1.11.10](https://github.com/FlyBodies/fly-fit/compare/v1.11.9...v1.11.10) (2024-08-08)


### Bug Fixes

* Implemented pure transactional logic for updating organizations ([#129](https://github.com/FlyBodies/fly-fit/issues/129)) ([3b5b068](https://github.com/FlyBodies/fly-fit/commit/3b5b0683d407254cf4eeeee789a56cf56ce9758a))

## [1.11.9](https://github.com/FlyBodies/fly-fit/compare/v1.11.8...v1.11.9) (2024-08-08)


### Bug Fixes

* Fixed organization update and other small UI fixes ([#128](https://github.com/FlyBodies/fly-fit/issues/128)) ([ec44eaa](https://github.com/FlyBodies/fly-fit/commit/ec44eaa09622bff4c0e635b714b790ac00eca405))

## [1.11.8](https://github.com/FlyBodies/fly-fit/compare/v1.11.7...v1.11.8) (2024-08-07)


### Bug Fixes

* Add indexes to source control ([8cca952](https://github.com/FlyBodies/fly-fit/commit/8cca9526efe56828ef69843540ab59191602db7c))

## [1.11.7](https://github.com/FlyBodies/fly-fit/compare/v1.11.6...v1.11.7) (2024-08-07)


### Bug Fixes

* Added account creation email ([#126](https://github.com/FlyBodies/fly-fit/issues/126)) ([554903b](https://github.com/FlyBodies/fly-fit/commit/554903b5be4223a23cfc5535b36c0a013286eb4f))
* Added challenge check in notifications ([#127](https://github.com/FlyBodies/fly-fit/issues/127)) ([e4fb891](https://github.com/FlyBodies/fly-fit/commit/e4fb891e4fd751de62ad1fa5b9533922f5df5703))
* Added trainer edit client, and client user reflects a trainers organization list ([#125](https://github.com/FlyBodies/fly-fit/issues/125)) ([369cc03](https://github.com/FlyBodies/fly-fit/commit/369cc038232fa2d4bf22372a0b26057a7b6050d6))
* Made transaction updates not use async/await ([9bfa772](https://github.com/FlyBodies/fly-fit/commit/9bfa772830bf3f819e4d3b108daa21375b597be9))

## [1.11.6](https://github.com/FlyBodies/fly-fit/compare/v1.11.5...v1.11.6) (2024-08-02)


### Bug Fixes

* Added ability for trainers to create users ([#124](https://github.com/FlyBodies/fly-fit/issues/124)) ([cb15bb1](https://github.com/FlyBodies/fly-fit/commit/cb15bb158e510401455319265ad92963e3c5133e))

## [1.11.5](https://github.com/FlyBodies/fly-fit/compare/v1.11.4...v1.11.5) (2024-08-01)


### Bug Fixes

* Added Android read-sync with weight data ([#123](https://github.com/FlyBodies/fly-fit/issues/123)) ([859e47d](https://github.com/FlyBodies/fly-fit/commit/859e47da029c056224e4a7442dc0de36b7ac6ef5))
* Fixed Android notifications ([#122](https://github.com/FlyBodies/fly-fit/issues/122)) ([a71f71e](https://github.com/FlyBodies/fly-fit/commit/a71f71e500fec3fbecb4730b5e95b7d879f87d47))

## [1.11.4](https://github.com/FlyBodies/fly-fit/compare/v1.11.3...v1.11.4) (2024-07-31)


### Bug Fixes

* Mileage challenge updates, other small app feedback  ([#121](https://github.com/FlyBodies/fly-fit/issues/121)) ([eed1f3f](https://github.com/FlyBodies/fly-fit/commit/eed1f3fb760464b3a309b411619c662011b3b764))

## [1.11.3](https://github.com/FlyBodies/fly-fit/compare/v1.11.2...v1.11.3) (2024-07-30)


### Bug Fixes

* Patch create/update user auth not having permission ([039bea2](https://github.com/FlyBodies/fly-fit/commit/039bea2814aebeb5fda98b4380c09c2c1f9817b4))
* PDF updates for mileage challenge report ([#120](https://github.com/FlyBodies/fly-fit/issues/120)) ([a629f23](https://github.com/FlyBodies/fly-fit/commit/a629f23b4d16e4fcdf35ddf48aea13ae822bfb64))

## [1.11.2](https://github.com/FlyBodies/fly-fit/compare/v1.11.1...v1.11.2) (2024-07-26)


### Bug Fixes

* Android fixes, email updates, summary report fixes ([#119](https://github.com/FlyBodies/fly-fit/issues/119)) ([df1f6fd](https://github.com/FlyBodies/fly-fit/commit/df1f6fd5e2c4b794e1621f8b72950a1d9ac61fb7))
* Vertical align top for TextInput android & ios ([b0a92ce](https://github.com/FlyBodies/fly-fit/commit/b0a92ce76a3168c4bac2d6f5ee7828e11594ffe4))

## [1.11.1](https://github.com/FlyBodies/fly-fit/compare/v1.11.0...v1.11.1) (2024-07-26)


### Bug Fixes

* Added mileage challenge summary report type to app ([#118](https://github.com/FlyBodies/fly-fit/issues/118)) ([516f9e6](https://github.com/FlyBodies/fly-fit/commit/516f9e6811323761b2258a67ecccfec8842999f9))
* Small mileage challenge report email template updates ([8bdd571](https://github.com/FlyBodies/fly-fit/commit/8bdd57177bf619d5c1d3e1974b3c28201a3d0d8f))

# [1.11.0](https://github.com/FlyBodies/fly-fit/compare/v1.10.3...v1.11.0) (2024-07-24)


### Bug Fixes

* Fix install all in ci-cd script, added build to prelint command ([3c60e6f](https://github.com/FlyBodies/fly-fit/commit/3c60e6f7f5b32bb76ea69eb2bae349b79223449d))


### Features

* Added the organization feed tab ([#117](https://github.com/FlyBodies/fly-fit/issues/117)) ([9feab83](https://github.com/FlyBodies/fly-fit/commit/9feab833171e8b06d0cb44487b3c75e324c61d31))

## [1.10.3](https://github.com/FlyBodies/fly-fit/compare/v1.10.2...v1.10.3) (2024-07-22)


### Bug Fixes

* Fix android health connect permissions issue ([671e59d](https://github.com/FlyBodies/fly-fit/commit/671e59d34ead2d41105540931e53a33b8e59f329))

## [1.10.2](https://github.com/FlyBodies/fly-fit/compare/v1.10.1...v1.10.2) (2024-07-22)


### Bug Fixes

* Added apple health screenshot for showing permissions ([f89bb20](https://github.com/FlyBodies/fly-fit/commit/f89bb205e3f017292e514da16c73c6a75a9f0bac))

## [1.10.1](https://github.com/FlyBodies/fly-fit/compare/v1.10.0...v1.10.1) (2024-07-20)


### Bug Fixes

* Added valid date range restrictions on create summary report ([dc53e9b](https://github.com/FlyBodies/fly-fit/commit/dc53e9b40fd841b864f0b1eeaff5c62ef1778d71))
* Cleanup up email verbiage and fixed email links ([8ae08d9](https://github.com/FlyBodies/fly-fit/commit/8ae08d90899ea33b56e181a323a1f0d862154961))

# [1.10.0](https://github.com/FlyBodies/fly-fit/compare/v1.9.4...v1.10.0) (2024-07-19)


### Features

* Added in-app Summary Reports ([#113](https://github.com/FlyBodies/fly-fit/issues/113)) ([eba2a52](https://github.com/FlyBodies/fly-fit/commit/eba2a525ff8c8fd7adf740ef4ffd0048ba6750c3))

## [1.9.4](https://github.com/FlyBodies/fly-fit/compare/v1.9.3...v1.9.4) (2024-07-17)


### Bug Fixes

* Add report generation to frontend ([#116](https://github.com/FlyBodies/fly-fit/issues/116)) ([7c630f2](https://github.com/FlyBodies/fly-fit/commit/7c630f263d52b74c7c356a87b3cbde3ad1c410a1))
* Fix the weight sample override algorithm ([337ef76](https://github.com/FlyBodies/fly-fit/commit/337ef764a3797d919e7e5d97e066a05c8e5fb310))

## [1.9.3](https://github.com/FlyBodies/fly-fit/compare/v1.9.2...v1.9.3) (2024-07-15)


### Bug Fixes

* Fixing health sync loading, performance, and pop-up issues ([#115](https://github.com/FlyBodies/fly-fit/issues/115)) ([5efee9f](https://github.com/FlyBodies/fly-fit/commit/5efee9f759c68ab9bf7917775c041862aea10fa5))

## [1.9.2](https://github.com/FlyBodies/fly-fit/compare/v1.9.1...v1.9.2) (2024-07-13)


### Bug Fixes

* Improvements and fixes for weight feature ([#114](https://github.com/FlyBodies/fly-fit/issues/114)) ([4c322fa](https://github.com/FlyBodies/fly-fit/commit/4c322fa1764f2058f662fefb4d81f68f4991921a))

## [1.9.1](https://github.com/FlyBodies/fly-fit/compare/v1.9.0...v1.9.1) (2024-07-11)


### Bug Fixes

* Package.lock change ([3135c50](https://github.com/FlyBodies/fly-fit/commit/3135c50c77d533d4d1a59e247fab631409bc46df))

# [1.9.0](https://github.com/FlyBodies/fly-fit/compare/v1.8.7...v1.9.0) (2024-07-11)


### Features

* Added weight tracking feature ([#112](https://github.com/FlyBodies/fly-fit/issues/112)) ([dac1cc6](https://github.com/FlyBodies/fly-fit/commit/dac1cc6b02811467bb140a10056ff017fe9a81c3))

## [1.8.7](https://github.com/FlyBodies/fly-fit/compare/v1.8.6...v1.8.7) (2024-07-09)


### Bug Fixes

* Added deep-equal dependency to functions/ ([577912c](https://github.com/FlyBodies/fly-fit/commit/577912cc2157f104e1572d7fb4f805a8c43349c1))

## [1.8.6](https://github.com/FlyBodies/fly-fit/compare/v1.8.5...v1.8.6) (2024-07-09)


### Bug Fixes

* Added Android data source pop up window ([#110](https://github.com/FlyBodies/fly-fit/issues/110)) ([bde417d](https://github.com/FlyBodies/fly-fit/commit/bde417dbfca409e184163467621655510eebeb9d))
* Fix health stat override algorithm to be more rigorous and consistent across front/backend ([#111](https://github.com/FlyBodies/fly-fit/issues/111)) ([2f8b333](https://github.com/FlyBodies/fly-fit/commit/2f8b3338c4bcf96a3e3990f510b3427632863981))

## [1.8.5](https://github.com/FlyBodies/fly-fit/compare/v1.8.4...v1.8.5) (2024-07-08)


### Bug Fixes

* Android fix and login fix ([#109](https://github.com/FlyBodies/fly-fit/issues/109)) ([e14f5e1](https://github.com/FlyBodies/fly-fit/commit/e14f5e186d6e49beaee5c2c5f1d4ecfcb6ab6949))
* Fixed typo in push notification message ([32c4b21](https://github.com/FlyBodies/fly-fit/commit/32c4b21816202e063408bb89916d954e9ddcf19d))

## [1.8.4](https://github.com/FlyBodies/fly-fit/compare/v1.8.3...v1.8.4) (2024-07-06)


### Bug Fixes

* Added challenge milestone push notifications, added swagger ([#108](https://github.com/FlyBodies/fly-fit/issues/108)) ([fd538a9](https://github.com/FlyBodies/fly-fit/commit/fd538a9300ec27b72d13aec510a2e115df64f01f))
* Edit workout datetime bug fix, trainer summary completed workouts, selected day bigger, tech debt clean up ([#107](https://github.com/FlyBodies/fly-fit/issues/107)) ([740bdea](https://github.com/FlyBodies/fly-fit/commit/740bdea3137cba52671e5155259f7a378c4cdd52))
* Revert TS version to stable ([55a7c19](https://github.com/FlyBodies/fly-fit/commit/55a7c196b6a938ab282f148de9edf82f5abff5d0))

## [1.8.3](https://github.com/FlyBodies/fly-fit/compare/v1.8.2...v1.8.3) (2024-07-03)


### Bug Fixes

* Add duplicate workout feature ([#105](https://github.com/FlyBodies/fly-fit/issues/105)) ([83ff106](https://github.com/FlyBodies/fly-fit/commit/83ff106eca0ccc2ca228182efc2d93d953ec7181))
* Added challenge milestone pop up ([#104](https://github.com/FlyBodies/fly-fit/issues/104)) ([fd07353](https://github.com/FlyBodies/fly-fit/commit/fd07353b02874205d9496a9a2ca318767c83ff19))
* Challenge calculation fix to only include strictly inclusive HealthStats ([#103](https://github.com/FlyBodies/fly-fit/issues/103)) ([c072040](https://github.com/FlyBodies/fly-fit/commit/c0720405864cc0aac04e5d7e98d7779e81959b79))
* Normalization of Android sourced Health Connect data ([#106](https://github.com/FlyBodies/fly-fit/issues/106)) ([e933a26](https://github.com/FlyBodies/fly-fit/commit/e933a26553b8460740301ab816502b6ed4760510))
* Report generation updated 7 day format and style ([#102](https://github.com/FlyBodies/fly-fit/issues/102)) ([058ed0b](https://github.com/FlyBodies/fly-fit/commit/058ed0b5100004c3801416eeafbb519b4aa9fd90))

## [1.8.2](https://github.com/FlyBodies/fly-fit/compare/v1.8.1...v1.8.2) (2024-06-28)


### Bug Fixes

* Fix search to allow undefined values ([bd0abe3](https://github.com/FlyBodies/fly-fit/commit/bd0abe35d47036075b335251c55427ff760163e6))

## [1.8.1](https://github.com/FlyBodies/fly-fit/compare/v1.8.0...v1.8.1) (2024-06-28)


### Bug Fixes

* Organization search fixes, notification fixes ([#101](https://github.com/FlyBodies/fly-fit/issues/101)) ([5ef140c](https://github.com/FlyBodies/fly-fit/commit/5ef140cabd6218afc1b683f916c99643e6ba5981))

# [1.8.0](https://github.com/FlyBodies/fly-fit/compare/v1.7.3...v1.8.0) (2024-06-26)


### Bug Fixes

* Added better user/org search and improved onDocumentUpdate backend functions ([#100](https://github.com/FlyBodies/fly-fit/issues/100)) ([a4be754](https://github.com/FlyBodies/fly-fit/commit/a4be75478a106b63117e4e2077c6d7b5d3e1a403))


### Features

* Added admin tab with organization management ([#99](https://github.com/FlyBodies/fly-fit/issues/99)) ([6ce7ef6](https://github.com/FlyBodies/fly-fit/commit/6ce7ef6bc8054fe9c1f0117c6dd6da176910e580))

## [1.7.3](https://github.com/FlyBodies/fly-fit/compare/v1.7.2...v1.7.3) (2024-06-23)


### Bug Fixes

* Added notification pop-up modal prompt ([#98](https://github.com/FlyBodies/fly-fit/issues/98)) ([2f7e1b1](https://github.com/FlyBodies/fly-fit/commit/2f7e1b19a75b6dd74590c1804c3c561befdb26b8))

## [1.7.2](https://github.com/FlyBodies/fly-fit/compare/v1.7.1...v1.7.2) (2024-06-21)


### Bug Fixes

* Added timezone formatting to server local date ([59f3a29](https://github.com/FlyBodies/fly-fit/commit/59f3a2947eb0bef4974857c990f3210a444d3b93))

## [1.7.1](https://github.com/FlyBodies/fly-fit/compare/v1.7.0...v1.7.1) (2024-06-21)


### Bug Fixes

* Added push notifications for workout creation events ([#97](https://github.com/FlyBodies/fly-fit/issues/97)) ([201771c](https://github.com/FlyBodies/fly-fit/commit/201771c1016b9d82e7f09de1e306fa2b28544992))

# [1.7.0](https://github.com/FlyBodies/fly-fit/compare/v1.6.24...v1.7.0) (2024-06-21)


### Bug Fixes

* Added completion status to participants in workout view ([#96](https://github.com/FlyBodies/fly-fit/issues/96)) ([89a8bd8](https://github.com/FlyBodies/fly-fit/commit/89a8bd898f8ca5f0ed858ee6ecd5778d1acd4126))


### Features

* Added PDF Report Generation feature to codebase ([#89](https://github.com/FlyBodies/fly-fit/issues/89)) ([49c4433](https://github.com/FlyBodies/fly-fit/commit/49c443362d8c43b99601bcf8b9843e3cfe665060))

## [1.6.24](https://github.com/FlyBodies/fly-fit/compare/v1.6.23...v1.6.24) (2024-06-14)


### Bug Fixes

* Expo doctor fixes ([4851ac7](https://github.com/FlyBodies/fly-fit/commit/4851ac7da91f05157f2578a8f1b6adc0dd6bd8d7))
* Fix showing the workout arm icon with correct color for trainers ([eeac5e1](https://github.com/FlyBodies/fly-fit/commit/eeac5e1e1a829ba44b6645049474e872e6ea86e8))

## [1.6.23](https://github.com/FlyBodies/fly-fit/compare/v1.6.22...v1.6.23) (2024-06-13)


### Bug Fixes

* Sprint 3 feedback and other minor fixes ([#94](https://github.com/FlyBodies/fly-fit/issues/94)) ([5ad0738](https://github.com/FlyBodies/fly-fit/commit/5ad0738f09f420974408a416768e3eac3e290f2f))

## [1.6.22](https://github.com/FlyBodies/fly-fit/compare/v1.6.21...v1.6.22) (2024-06-12)


### Bug Fixes

* Add the "links" field to workout ([#91](https://github.com/FlyBodies/fly-fit/issues/91)) ([494fb88](https://github.com/FlyBodies/fly-fit/commit/494fb884633f7b688f95d02615233610dee476ab))
* Add workout photo upload and enhanced workout link editing ([#92](https://github.com/FlyBodies/fly-fit/issues/92)) ([033fdfe](https://github.com/FlyBodies/fly-fit/commit/033fdfe6cdf235bc323e3be2fd5886371295bcd5))
* Nested workout view fix for coach, other UI fixes ([#93](https://github.com/FlyBodies/fly-fit/issues/93)) ([1fc7698](https://github.com/FlyBodies/fly-fit/commit/1fc7698a711fbf6914f616ec564589957d856a23))

## [1.6.21](https://github.com/FlyBodies/fly-fit/compare/v1.6.20...v1.6.21) (2024-06-06)


### Bug Fixes

* Workout confirmation related fixes ([#90](https://github.com/FlyBodies/fly-fit/issues/90)) ([9f0aac0](https://github.com/FlyBodies/fly-fit/commit/9f0aac0ae4bdec00c90adfdbfb7787c1e7ddf432))

## [1.6.20](https://github.com/FlyBodies/fly-fit/compare/v1.6.19...v1.6.20) (2024-06-05)


### Bug Fixes

* Small workout completion status fixes ([#88](https://github.com/FlyBodies/fly-fit/issues/88)) ([2e3e22a](https://github.com/FlyBodies/fly-fit/commit/2e3e22a3ef1d04dbd2faa2e67c66ec40dc0a607d))

## [1.6.19](https://github.com/FlyBodies/fly-fit/compare/v1.6.18...v1.6.19) (2024-06-05)


### Bug Fixes

* Added new workout types ([#86](https://github.com/FlyBodies/fly-fit/issues/86)) ([a04de7a](https://github.com/FlyBodies/fly-fit/commit/a04de7a6bc401cbc6e08e0fcccb4ae0619e356fa))
* Added workout confirmation feature ([#87](https://github.com/FlyBodies/fly-fit/issues/87)) ([23727ef](https://github.com/FlyBodies/fly-fit/commit/23727ef57db65390d6f012efd53c727a53c0ce1d))

## [1.6.18](https://github.com/FlyBodies/fly-fit/compare/v1.6.17...v1.6.18) (2024-06-01)


### Bug Fixes

* Streak updates, screenshots updated, goal uses steps, profile update bug fix, live countdown fix ([#85](https://github.com/FlyBodies/fly-fit/issues/85)) ([4ed17fc](https://github.com/FlyBodies/fly-fit/commit/4ed17fcb6735fda0df0d301d296ac78b0d9e34c8))

## [1.6.17](https://github.com/FlyBodies/fly-fit/compare/v1.6.16...v1.6.17) (2024-05-31)


### Bug Fixes

* Streak fixes and other documentation updates ([#84](https://github.com/FlyBodies/fly-fit/issues/84)) ([f14a1a5](https://github.com/FlyBodies/fly-fit/commit/f14a1a521a0c43c942e84e1a33fa00fc0d8e6441))

## [1.6.16](https://github.com/FlyBodies/fly-fit/compare/v1.6.15...v1.6.16) (2024-05-30)


### Bug Fixes

* Streak enhancements, fixes, challenge/workout datetime fixes, workout card fixes ([#83](https://github.com/FlyBodies/fly-fit/issues/83)) ([92046da](https://github.com/FlyBodies/fly-fit/commit/92046da0aaa00863f3985ef5664d5779b98ca2b5))

## [1.6.15](https://github.com/FlyBodies/fly-fit/compare/v1.6.14...v1.6.15) (2024-05-29)


### Bug Fixes

* Patching fix where stats were not updating for user ([194826c](https://github.com/FlyBodies/fly-fit/commit/194826cf8c804215c0eec00bbca9ca5537e2f8a3))

## [1.6.14](https://github.com/FlyBodies/fly-fit/compare/v1.6.13...v1.6.14) (2024-05-29)


### Bug Fixes

* Added streak and other summary numbers to user home page ([#82](https://github.com/FlyBodies/fly-fit/issues/82)) ([b63d358](https://github.com/FlyBodies/fly-fit/commit/b63d3587efb53a80a8b0f60f104a63552a60b998))
* Backend streak calculation fixes ([#81](https://github.com/FlyBodies/fly-fit/issues/81)) ([2295db8](https://github.com/FlyBodies/fly-fit/commit/2295db837d93b342a08d65ddb8345cff70fec233))
* Fix npm install esbuild error when building ([689924c](https://github.com/FlyBodies/fly-fit/commit/689924cbb6152b7fc88b889640290c0514f9c9cb))

## [1.6.13](https://github.com/FlyBodies/fly-fit/compare/v1.6.12...v1.6.13) (2024-05-24)


### Bug Fixes

* Remove labels from firebase functions tagging ([7437c24](https://github.com/FlyBodies/fly-fit/commit/7437c2445772015fbf49de481f6e7cc9699b75e3))

## [1.6.12](https://github.com/FlyBodies/fly-fit/compare/v1.6.11...v1.6.12) (2024-05-24)


### Bug Fixes

* Clean script to remove lib, fix unit tests only running TS files ([ce1de39](https://github.com/FlyBodies/fly-fit/commit/ce1de39f38a2c3ed9048d60b60227fbd554f62c3))
* Rever Expo to SDK 50 ([#80](https://github.com/FlyBodies/fly-fit/issues/80)) ([8896d16](https://github.com/FlyBodies/fly-fit/commit/8896d162a94f2b8e7b03b9b6d67e643e7bd9f87e))

## [1.6.11](https://github.com/FlyBodies/fly-fit/compare/v1.6.10...v1.6.11) (2024-05-22)


### Bug Fixes

* Capitalization error of png filename ([4cfe362](https://github.com/FlyBodies/fly-fit/commit/4cfe3629fe21cdddfbb761d6c812d7209303d5b1))

## [1.6.10](https://github.com/FlyBodies/fly-fit/compare/v1.6.9...v1.6.10) (2024-05-22)


### Bug Fixes

* Capitalization error in png filename ([a0686d7](https://github.com/FlyBodies/fly-fit/commit/a0686d786ad62f2514812dc629fe1449891f991f))

## [1.6.9](https://github.com/FlyBodies/fly-fit/compare/v1.6.8...v1.6.9) (2024-05-22)


### Bug Fixes

* Initialized app engine default and upgraded deps ([0582324](https://github.com/FlyBodies/fly-fit/commit/05823243919bd5f9707d5af5790ab5a3e6ee02c6))

## [1.6.8](https://github.com/FlyBodies/fly-fit/compare/v1.6.7...v1.6.8) (2024-05-22)


### Bug Fixes

* Coach tab enhancements and crash fixes ([#79](https://github.com/FlyBodies/fly-fit/issues/79)) ([2995fbc](https://github.com/FlyBodies/fly-fit/commit/2995fbc07e0f83ee26beee74771fd9e8a4ab585d))

## [1.6.7](https://github.com/FlyBodies/fly-fit/compare/v1.6.6...v1.6.7) (2024-05-15)


### Bug Fixes

* Added coach tab ([#78](https://github.com/FlyBodies/fly-fit/issues/78)) ([8abaea4](https://github.com/FlyBodies/fly-fit/commit/8abaea4010b417025b8885ce546e3f4f28fa356a))
* Expo 51 upgrade ([81f7085](https://github.com/FlyBodies/fly-fit/commit/81f7085959f2f2403f4df3218b5349539537f505))
* Expo 51 upgrade patches ([c903e46](https://github.com/FlyBodies/fly-fit/commit/c903e46bd57eab466e7f9239187c2fd3564a9b41))
* Upgrade health-connect to v2 ([f24c0a3](https://github.com/FlyBodies/fly-fit/commit/f24c0a3003ec875d7759a36c64f981e85fee63e1))

## [1.6.6](https://github.com/FlyBodies/fly-fit/compare/v1.6.5...v1.6.6) (2024-05-07)


### Bug Fixes

* Fix "Done" key on keyboard, and selecting text showing done button ([9720638](https://github.com/FlyBodies/fly-fit/commit/972063858122b1d88f7c1b6692fb67f11b21033b))
* Update EditWorkout to use live value instead of initial state ([96d2704](https://github.com/FlyBodies/fly-fit/commit/96d2704aa57061e3e0b060d966c0357155e17afe))

## [1.6.5](https://github.com/FlyBodies/fly-fit/compare/v1.6.4...v1.6.5) (2024-05-06)


### Bug Fixes

* Added workout copying and other workout enhancements ([#77](https://github.com/FlyBodies/fly-fit/issues/77)) ([a7a2157](https://github.com/FlyBodies/fly-fit/commit/a7a21578a81efcdc11efeb94bc108647f5901120))

## [1.6.4](https://github.com/FlyBodies/fly-fit/compare/v1.6.3...v1.6.4) (2024-04-29)


### Bug Fixes

* Add ability for teachers to create workouts ([#76](https://github.com/FlyBodies/fly-fit/issues/76)) ([4a59a8d](https://github.com/FlyBodies/fly-fit/commit/4a59a8d1d4e1276da5625e3f5b7241f689a94359))

## [1.6.3](https://github.com/FlyBodies/fly-fit/compare/v1.6.2...v1.6.3) (2024-04-23)


### Bug Fixes

* Remove the private profile feature ([2a153e0](https://github.com/FlyBodies/fly-fit/commit/2a153e0399d9077e5e7e781ed38c82869ee92f67))

## [1.6.2](https://github.com/FlyBodies/fly-fit/compare/v1.6.1...v1.6.2) (2024-04-22)


### Bug Fixes

* Disabled teachers create workout until it is ready ([047ddb3](https://github.com/FlyBodies/fly-fit/commit/047ddb3b1581c6fa1fd021da0b724b22428d8dc0))

## [1.6.1](https://github.com/FlyBodies/fly-fit/compare/v1.6.0...v1.6.1) (2024-04-22)


### Bug Fixes

* Added collection summary endpoint ([9a9e7d5](https://github.com/FlyBodies/fly-fit/commit/9a9e7d5ce639a52ba24143041d47bacffd7a8e40))
* Added daily notification date filtering and paper dates ([0573446](https://github.com/FlyBodies/fly-fit/commit/05734461f7722e78b124d6729c9959ece3bc98e1))
* Fixed the create user flow on create user page ([#75](https://github.com/FlyBodies/fly-fit/issues/75)) ([cb5f8d5](https://github.com/FlyBodies/fly-fit/commit/cb5f8d5dad711184fcdd74eb27384c9e30a5b9e6))

# [1.6.0](https://github.com/FlyBodies/fly-fit/compare/v1.5.26...v1.6.0) (2024-04-11)


### Features

* Add push notifications about wellness ([#74](https://github.com/FlyBodies/fly-fit/issues/74)) ([2960a6b](https://github.com/FlyBodies/fly-fit/commit/2960a6bebe921b51aeff1bcc6c2179d637c94e04))

## [1.5.26](https://github.com/FlyBodies/fly-fit/compare/v1.5.25...v1.5.26) (2024-03-26)


### Bug Fixes

* Implement dynamic goal updates when step length changes and other small fixes ([#73](https://github.com/FlyBodies/fly-fit/issues/73)) ([bba0b5c](https://github.com/FlyBodies/fly-fit/commit/bba0b5cad4ddb05ecfe1d3b3bea2e5e76a10375b))

## [1.5.25](https://github.com/FlyBodies/fly-fit/compare/v1.5.24...v1.5.25) (2024-03-25)


### Bug Fixes

* Implemented goal usage on home page ([eed93f6](https://github.com/FlyBodies/fly-fit/commit/eed93f606dee71e06628a5c13386c06cc300d054))

## [1.5.24](https://github.com/FlyBodies/fly-fit/compare/v1.5.23...v1.5.24) (2024-03-24)


### Bug Fixes

* Minor update eas version ([5cbe828](https://github.com/FlyBodies/fly-fit/commit/5cbe828baaa79bb693546bb89bd3281b4599b5d3))

## [1.5.23](https://github.com/FlyBodies/fly-fit/compare/v1.5.22...v1.5.23) (2024-03-24)


### Bug Fixes

* Added profile goals, lint consistency fixes, package updates ([#72](https://github.com/FlyBodies/fly-fit/issues/72)) ([d268ef0](https://github.com/FlyBodies/fly-fit/commit/d268ef01b703c22c3628109df263802d2fffb217))

## [1.5.22](https://github.com/FlyBodies/fly-fit/compare/v1.5.21...v1.5.22) (2024-03-13)


### Bug Fixes

* Fix step length not changing when height changed, attempt fix expo-asset ([d3384a9](https://github.com/FlyBodies/fly-fit/commit/d3384a9ea4ac27af14b90ec885106e8b08dd99cd))

## [1.5.21](https://github.com/FlyBodies/fly-fit/compare/v1.5.20...v1.5.21) (2024-03-12)


### Bug Fixes

* Fix bug of step length not using new number, and UI bug ([2922562](https://github.com/FlyBodies/fly-fit/commit/2922562daeaab3b621fa4a977170bb70bb5a1c40))
* Uninstall dropdown library in favor of local component ([1f6988a](https://github.com/FlyBodies/fly-fit/commit/1f6988a4dbd7313629b2595fb53c3b4f7c0d89fd))

## [1.5.20](https://github.com/FlyBodies/fly-fit/compare/v1.5.19...v1.5.20) (2024-03-12)


### Bug Fixes

* Implemented suggested feedback changes for height on edit profile ([#71](https://github.com/FlyBodies/fly-fit/issues/71)) ([7d24ba1](https://github.com/FlyBodies/fly-fit/commit/7d24ba13a8dfdf953fbc2575f9f12b88dd00b6d7))

## [1.5.19](https://github.com/FlyBodies/fly-fit/compare/v1.5.18...v1.5.19) (2024-03-11)


### Bug Fixes

* Ensured override image URL takes precedence over local image ([85f9d67](https://github.com/FlyBodies/fly-fit/commit/85f9d67d1c63da4176a6a7470aa33068b01a2444))

## [1.5.18](https://github.com/FlyBodies/fly-fit/compare/v1.5.17...v1.5.18) (2024-03-11)


### Bug Fixes

* Patch package RN datetimepicker ([7529350](https://github.com/FlyBodies/fly-fit/commit/752935040e025a5f2496edcf65ca86087bdfe44f))

## [1.5.17](https://github.com/FlyBodies/fly-fit/compare/v1.5.16...v1.5.17) (2024-03-11)


### Bug Fixes

* Added height option in edit user profile for step length ([#70](https://github.com/FlyBodies/fly-fit/issues/70)) ([4774975](https://github.com/FlyBodies/fly-fit/commit/4774975b2c21091c890f2c0ddf642c4f098feba0))
* Fixed avgMileage/Steps per challenge to exclude participants ([fbf5dce](https://github.com/FlyBodies/fly-fit/commit/fbf5dce33596fc893e2400c13ac6d88f964b23f1))

## [1.5.16](https://github.com/FlyBodies/fly-fit/compare/v1.5.15...v1.5.16) (2024-03-03)


### Bug Fixes

* Fix challenge cover photo, change default profile image ([37a14c0](https://github.com/FlyBodies/fly-fit/commit/37a14c01f84103bb251897de03a1995066a702f3))

## [1.5.15](https://github.com/FlyBodies/fly-fit/compare/v1.5.14...v1.5.15) (2024-03-01)


### Bug Fixes

* Changed challenge cover photos to static URLs ([8450bd2](https://github.com/FlyBodies/fly-fit/commit/8450bd2921e394e2aa79f645ab8fef34eb84aa89))

## [1.5.14](https://github.com/FlyBodies/fly-fit/compare/v1.5.13...v1.5.14) (2024-03-01)


### Bug Fixes

* Add step challenge type ([#69](https://github.com/FlyBodies/fly-fit/issues/69)) ([0e6cf89](https://github.com/FlyBodies/fly-fit/commit/0e6cf899c6d3e3907dc6df4e4275491d43f83ee1))

## [1.5.13](https://github.com/FlyBodies/fly-fit/compare/v1.5.12...v1.5.13) (2024-03-01)


### Bug Fixes

* Added static height to challenge header photo ([01ac2a6](https://github.com/FlyBodies/fly-fit/commit/01ac2a6e725ca375ad4f36eec88101f25f25f530))

## [1.5.12](https://github.com/FlyBodies/fly-fit/compare/v1.5.11...v1.5.12) (2024-03-01)


### Bug Fixes

* Revert blurhash change attempt ([3cec8f1](https://github.com/FlyBodies/fly-fit/commit/3cec8f188a20fb82feca86d6f82c90eedb7ba18f))

## [1.5.11](https://github.com/FlyBodies/fly-fit/compare/v1.5.10...v1.5.11) (2024-03-01)


### Bug Fixes

* Attempt at blurhash fix ([48caf21](https://github.com/FlyBodies/fly-fit/commit/48caf215a75118ef4c55d99f19eeb6fe426262be))
* Update deps, fix clean command ([18dbad2](https://github.com/FlyBodies/fly-fit/commit/18dbad2a5e8cda4a5da9fbcf78269f89c009aead))
* Update eas version, update challenge photo header ([322ac3f](https://github.com/FlyBodies/fly-fit/commit/322ac3f7c6091aff711d67070438f60695e5110f))

## [1.5.10](https://github.com/FlyBodies/fly-fit/compare/v1.5.9...v1.5.10) (2024-03-01)


### Bug Fixes

* Fix conditionally showing blurhash as placeholder while image loads ([e8e263f](https://github.com/FlyBodies/fly-fit/commit/e8e263ff7a1a77714e8d742883c9ed6fe99fd652))

## [1.5.9](https://github.com/FlyBodies/fly-fit/compare/v1.5.8...v1.5.9) (2024-03-01)


### Bug Fixes

* Added "steps as currency" feature ([#67](https://github.com/FlyBodies/fly-fit/issues/67)) ([b21bbd4](https://github.com/FlyBodies/fly-fit/commit/b21bbd46cddb6cee2305f0ebbf24ed344c533526))
* Fix image loading with placeholders ([#68](https://github.com/FlyBodies/fly-fit/issues/68)) ([f02f56f](https://github.com/FlyBodies/fly-fit/commit/f02f56f5fb1a67f115fbc6db36cca38d801d9099))

## [1.5.8](https://github.com/FlyBodies/fly-fit/compare/v1.5.7...v1.5.8) (2024-02-29)


### Bug Fixes

* Added private profile feature ([#66](https://github.com/FlyBodies/fly-fit/issues/66)) ([ffb883b](https://github.com/FlyBodies/fly-fit/commit/ffb883bc3d2a5798225f17c311280279c6cd4d0a))

## [1.5.7](https://github.com/FlyBodies/fly-fit/compare/v1.5.6...v1.5.7) (2024-02-29)


### Bug Fixes

* Added challengeDataExport endpoint ([#62](https://github.com/FlyBodies/fly-fit/issues/62)) ([713c945](https://github.com/FlyBodies/fly-fit/commit/713c945320c4441c2b6a9f2901cfb0af61be995d))
* Fix update app config script and ensured prod config up to date ([797bbeb](https://github.com/FlyBodies/fly-fit/commit/797bbeb9f1b6e4321b0817ad4eded110350dad11))
* Fixed challenges updating on backend if ended within 7 days ([#64](https://github.com/FlyBodies/fly-fit/issues/64)) ([a84dcd6](https://github.com/FlyBodies/fly-fit/commit/a84dcd630836744dbda4d9fda76d6ae83e5c7506))
* Sync behaviour default to 7 days, fixed app user crash, removed unused app settings buttons ([#65](https://github.com/FlyBodies/fly-fit/issues/65)) ([2a36046](https://github.com/FlyBodies/fly-fit/commit/2a36046f53ff1808b16445e5c5e884d69aaef10d))
* Update the challengeDataExport endpoint with new requirements ([#63](https://github.com/FlyBodies/fly-fit/issues/63)) ([d94e957](https://github.com/FlyBodies/fly-fit/commit/d94e957645c012b9e24f51723bc63ca25ed5b65d))

## [1.5.7](https://github.com/FlyBodies/fly-fit/compare/v1.5.6...v1.5.7) (2024-02-29)


### Bug Fixes

* Added challengeDataExport endpoint ([#62](https://github.com/FlyBodies/fly-fit/issues/62)) ([713c945](https://github.com/FlyBodies/fly-fit/commit/713c945320c4441c2b6a9f2901cfb0af61be995d))
* Fixed challenges updating on backend if ended within 7 days ([#64](https://github.com/FlyBodies/fly-fit/issues/64)) ([a84dcd6](https://github.com/FlyBodies/fly-fit/commit/a84dcd630836744dbda4d9fda76d6ae83e5c7506))
* Sync behaviour default to 7 days, fixed app user crash, removed unused app settings buttons ([#65](https://github.com/FlyBodies/fly-fit/issues/65)) ([2a36046](https://github.com/FlyBodies/fly-fit/commit/2a36046f53ff1808b16445e5c5e884d69aaef10d))
* Update the challengeDataExport endpoint with new requirements ([#63](https://github.com/FlyBodies/fly-fit/issues/63)) ([d94e957](https://github.com/FlyBodies/fly-fit/commit/d94e957645c012b9e24f51723bc63ca25ed5b65d))

## [1.5.6](https://github.com/FlyBodies/fly-fit/compare/v1.5.5...v1.5.6) (2024-02-14)


### Bug Fixes

* Change error throw into log warning ([027d1e6](https://github.com/FlyBodies/fly-fit/commit/027d1e614cb23fd184e300dabd433723eef3d8af))

## [1.5.5](https://github.com/FlyBodies/fly-fit/compare/v1.5.4...v1.5.5) (2024-02-14)


### Bug Fixes

* Firebase func env var fixes ([a6cc884](https://github.com/FlyBodies/fly-fit/commit/a6cc884cba04c3a44fbcc4f23f3101cf24901882))

## [1.5.4](https://github.com/FlyBodies/fly-fit/compare/v1.5.3...v1.5.4) (2024-02-14)


### Bug Fixes

* Dependency update, TS error fixes, lint fixes ([e67b8be](https://github.com/FlyBodies/fly-fit/commit/e67b8bec86a5dc42712d36dcaa13269e4dbbeddb))
* Revert portal host addition, cleanup spacing ([95f214e](https://github.com/FlyBodies/fly-fit/commit/95f214e73869e2f340be614c55a4b5053eafd03c))

## [1.5.3](https://github.com/FlyBodies/fly-fit/compare/v1.5.2...v1.5.3) (2024-02-14)


### Bug Fixes

* Commented out breaking babel config ([5014bc8](https://github.com/FlyBodies/fly-fit/commit/5014bc8d7a7dce9f335da08bb3a7b644845d6465))

## [1.5.2](https://github.com/FlyBodies/fly-fit/compare/v1.5.1...v1.5.2) (2024-02-13)


### Bug Fixes

* Added Portal.Host to ScreenWrapper component ([cf3451a](https://github.com/FlyBodies/fly-fit/commit/cf3451a71930c45a308515c31cb995e223852189))

## [1.5.1](https://github.com/FlyBodies/fly-fit/compare/v1.5.0...v1.5.1) (2024-02-13)


### Bug Fixes

* Added environment configs for firebase functions ([531b363](https://github.com/FlyBodies/fly-fit/commit/531b363e02a116a74f96bd3e9d35f8cc81520dbd))
* Update functions no challenges found to warnings, added back modify time ([5d2fcfb](https://github.com/FlyBodies/fly-fit/commit/5d2fcfba9b29df78218e7e2f3796c41556b556fd))
* Update sum miles/steps to truncate/round display ([b43a9cf](https://github.com/FlyBodies/fly-fit/commit/b43a9cfd8466b6963b8879c6418322f1624e6f35))

# [1.5.0](https://github.com/FlyBodies/fly-fit/compare/v1.4.5...v1.5.0) (2024-02-13)


### Features

* Revert to 1 hour intervals, perf improvements, profile page additions, challenge summary addition ([#61](https://github.com/FlyBodies/fly-fit/issues/61)) ([f251cec](https://github.com/FlyBodies/fly-fit/commit/f251cec4c043576c2a18386ff292946f0a69d597))

## [1.4.5](https://github.com/FlyBodies/fly-fit/compare/v1.4.4...v1.4.5) (2024-02-04)


### Bug Fixes

* Adjust firebase config settings and env:ci commands ([011a4e8](https://github.com/FlyBodies/fly-fit/commit/011a4e8edd833c3e58d4b972b3346422e21d2974))
* Fix challenge start sync bug, added challenge stats, made challenge progress more precise ([#60](https://github.com/FlyBodies/fly-fit/issues/60)) ([1ccbdf7](https://github.com/FlyBodies/fly-fit/commit/1ccbdf75a9d06d5196493c41a04ca2f7cac2ce77))
* Fixes to env:local npm commands ([c59bd37](https://github.com/FlyBodies/fly-fit/commit/c59bd37e37c71dbe36269a5566b5fee1b5d1c771))

## [1.4.4](https://github.com/FlyBodies/fly-fit/compare/v1.4.3...v1.4.4) (2024-01-30)


### Bug Fixes

* Cleaned up concurrently in package.json ([9201830](https://github.com/FlyBodies/fly-fit/commit/92018301937006e5c369f9c8de1a073bb4840845))
* Hid create challenge, fix apple logo ([a37fd7c](https://github.com/FlyBodies/fly-fit/commit/a37fd7cf66554e56ceee30399b0fc0c35ff554de))

## [1.4.3](https://github.com/FlyBodies/fly-fit/compare/v1.4.2...v1.4.3) (2024-01-30)


### Bug Fixes

* Added app store submission to README ([faebb35](https://github.com/FlyBodies/fly-fit/commit/faebb35b016326045e702cbbf1baa7d78e8ae075))
* Fix ISO date conversion, fix settings screen bug for update, fix iOS health screen link to open ([#59](https://github.com/FlyBodies/fly-fit/issues/59)) ([980577a](https://github.com/FlyBodies/fly-fit/commit/980577a2d94aee019a8a61f2258c03004a8ddfe8))

## [1.4.2](https://github.com/FlyBodies/fly-fit/compare/v1.4.1...v1.4.2) (2024-01-29)


### Bug Fixes

* Upgrade eas CLI version supported ([c357e91](https://github.com/FlyBodies/fly-fit/commit/c357e918e8893ac5586a1ce6ecf429d02a38ccac))

## [1.4.1](https://github.com/FlyBodies/fly-fit/compare/v1.4.0...v1.4.1) (2024-01-29)


### Bug Fixes

* Fix wrong profile for android prod local build ([933d84e](https://github.com/FlyBodies/fly-fit/commit/933d84e175a339c581eac3a70c59ece6c705bdb4))

# [1.4.0](https://github.com/FlyBodies/fly-fit/compare/v1.3.28...v1.4.0) (2024-01-29)


### Bug Fixes

* Converted PNG assets to WEBP ([0d1aa86](https://github.com/FlyBodies/fly-fit/commit/0d1aa86dfbe4687a7a2a6fd241f26d54ddcd89f1))
* Fix development build ci-cd script misnamed ([33a47ba](https://github.com/FlyBodies/fly-fit/commit/33a47baeba6d66572eb38d871c067d7aaa5d97d1))


### Features

* Expo upgrade, Implemented challenge UI redesign feature, other small UI fixes ([#58](https://github.com/FlyBodies/fly-fit/issues/58)) ([63c0335](https://github.com/FlyBodies/fly-fit/commit/63c0335a9d742608d824fa5283adac925a439672))

## [1.3.28](https://github.com/FlyBodies/fly-fit/compare/v1.3.27...v1.3.28) (2024-01-22)


### Bug Fixes

* Add contact and feedback links to app settings page ([078ea3c](https://github.com/FlyBodies/fly-fit/commit/078ea3cd7d574ba171d680898628a5dded8305db))

## [1.3.27](https://github.com/FlyBodies/fly-fit/compare/v1.3.26...v1.3.27) (2024-01-12)


### Bug Fixes

* Abstracted `validateIsOriginMain` script function ([65ffc68](https://github.com/FlyBodies/fly-fit/commit/65ffc68f1f5789b35e79baa091c83f918e93a8ba))
* Added analytics/deviceInfo logging values ([d7e04cd](https://github.com/FlyBodies/fly-fit/commit/d7e04cd57071a1410798ad424849f369545c9708))
* Made 8am UTC the default challenge start time ([#57](https://github.com/FlyBodies/fly-fit/issues/57)) ([0b98a1b](https://github.com/FlyBodies/fly-fit/commit/0b98a1b3992d8582aa3b319af2988f079fb9b6a3))

## [1.3.26](https://github.com/FlyBodies/fly-fit/compare/v1.3.25...v1.3.26) (2024-01-06)


### Bug Fixes

* Added background refresh, add concurrently ([37fb357](https://github.com/FlyBodies/fly-fit/commit/37fb357a9913497273e57379b3797f82c4ad9937))
* Added to README getting started documentation, Brewfile ([f79ff4b](https://github.com/FlyBodies/fly-fit/commit/f79ff4b4cc87b691601aef1e088e00f9ebc90a1f))
* Change eas production for android to app-bundle ([68d559b](https://github.com/FlyBodies/fly-fit/commit/68d559bac41804ffe38c6b38cce3b300e39952a5))
* Combine deploy & release flags for ci-cd script ([8ddf497](https://github.com/FlyBodies/fly-fit/commit/8ddf497a9af79e53747c7e7693716fa93df03e08))

## [1.3.25](https://github.com/FlyBodies/fly-fit/compare/v1.3.24...v1.3.25) (2024-01-05)


### Bug Fixes

* Made ci-cd script more modular with build& deploy for prod ([31aac10](https://github.com/FlyBodies/fly-fit/commit/31aac10cbe2743e40b736f15fc7cf6bbe9be76a4))

## [1.3.24](https://github.com/FlyBodies/fly-fit/compare/v1.3.23...v1.3.24) (2024-01-05)


### Bug Fixes

* Added expo-update logic to be manual only, revert update versn ([99a896e](https://github.com/FlyBodies/fly-fit/commit/99a896e516858bb646a44ded0450135d659ebe3c))
* Adjusted expo-updates to only run on error recovery ([79b17fd](https://github.com/FlyBodies/fly-fit/commit/79b17fd4afe424bfd3b95abf0f23c6dc5a411c6f))
* Fix ci-cd script to create tags properly, and execute build scripts ([ef15bd9](https://github.com/FlyBodies/fly-fit/commit/ef15bd97fe6d8235ce7f00d4255708140a217a13))
* Fix deploy scripts ([87ed56b](https://github.com/FlyBodies/fly-fit/commit/87ed56b1aa036e85af0f1ed747fd3735705e649d))
* Fix metro config, fix isDeploy ci-cd script ([ea52858](https://github.com/FlyBodies/fly-fit/commit/ea52858ec501859d9e2041002ac0cd07b9415e00))
* Fixed platformExecutor running in parrallel ([e0f67c7](https://github.com/FlyBodies/fly-fit/commit/e0f67c738ab44859bbf025d3a46f10fdb1c6ba48))
* Made clean script clean watchman more robustly ([4bc80b7](https://github.com/FlyBodies/fly-fit/commit/4bc80b796a215cbb662c6571fc8723ffb69b41a1))
* Prompt for release in ci-cd script ([c9e5588](https://github.com/FlyBodies/fly-fit/commit/c9e5588428a7f9b2470801c706e6b421f4fb3eac))
* Refactored ci-cd:scripts to support ios and android ([49c6ba6](https://github.com/FlyBodies/fly-fit/commit/49c6ba68316850e3b814dc70d7a82b64df034bbe))
* Remove early exit, move tagging to inside conditional check ([94c3f2a](https://github.com/FlyBodies/fly-fit/commit/94c3f2a8b509c36343083c432624282d4f63f6ba))

## [1.3.23](https://github.com/FlyBodies/fly-fit/compare/v1.3.22...v1.3.23) (2024-01-05)


### Bug Fixes

* Attempt to fix script issues ([1c3b20f](https://github.com/FlyBodies/fly-fit/commit/1c3b20febd7b7891e20f726e4847bdd4f6f55d3d))

## [1.3.22](https://github.com/FlyBodies/fly-fit/compare/v1.3.21...v1.3.22) (2024-01-05)


### Bug Fixes

* Add react-native-healt-connect dependency and implementation ([6b7dbc0](https://github.com/FlyBodies/fly-fit/commit/6b7dbc03620f3ff7b67c84d39e6123fd22f19d25))
* Completed adding MVP Health Connect, and made health syncing optional ([#55](https://github.com/FlyBodies/fly-fit/issues/55)) ([300ddc1](https://github.com/FlyBodies/fly-fit/commit/300ddc1c5d8966ef57e52e47b3b8b01350bef150))
* Default shell for eas build post install script ([811e7ad](https://github.com/FlyBodies/fly-fit/commit/811e7ad08b2d1bb17317b54f3ca09d70c1f23556))
* Fix eslint error ([f7612a6](https://github.com/FlyBodies/fly-fit/commit/f7612a6ab12c6d4a92ced358b1fa040a9ef72de3))
* Fixed missing firebase deploy config, improved logging on functions ([6723346](https://github.com/FlyBodies/fly-fit/commit/6723346dcb0fbbaf2ec37a7448344c0d2aab5376))
* Health connect patch, started android permission refactoring ([a35009d](https://github.com/FlyBodies/fly-fit/commit/a35009d00ccf3a272fc1cd49f8ff2652d7761200))
* Remove react-native-health-connect for android initial release ([9a04182](https://github.com/FlyBodies/fly-fit/commit/9a041829038fb3d102ecb51a4856c2fc246e7756))
* Small trim enhancements to import user scripts, success log added ([bd66731](https://github.com/FlyBodies/fly-fit/commit/bd66731c18e7592bce9d7c1107cb998a71567888))

## [1.3.22](https://github.com/FlyBodies/fly-fit/compare/v1.3.21...v1.3.22) (2023-12-24)


### Bug Fixes

* Default shell for eas build post install script ([17cd39b](https://github.com/FlyBodies/fly-fit/commit/17cd39b34b3cf363b101c9ea1a4278d0a0a62e47))

## [1.3.21](https://github.com/FlyBodies/fly-fit/compare/v1.3.20...v1.3.21) (2023-12-24)


### Bug Fixes

* Add setDefaultShell ([f44927b](https://github.com/FlyBodies/fly-fit/commit/f44927bf0e479cea4332941c6b5489d81b1416a2))

## [1.3.20](https://github.com/FlyBodies/fly-fit/compare/v1.3.19...v1.3.20) (2023-12-24)


### Bug Fixes

* ci-cd script fixes for android and ios ([2890858](https://github.com/FlyBodies/fly-fit/commit/28908589cf6ea80c9a0811654d56d9515940a7d7))

## [1.3.19](https://github.com/FlyBodies/fly-fit/compare/v1.3.18...v1.3.19) (2023-12-23)


### Bug Fixes

* Added graphic 512, reverted collection hook interface for users ([f2f2f96](https://github.com/FlyBodies/fly-fit/commit/f2f2f964ee7287296f88b6f8605ebf39ed78aff6))
* Android UI fixes ([d3c2ec7](https://github.com/FlyBodies/fly-fit/commit/d3c2ec750b1b929f97af08e2391963a507c4b72a))
* Challenges start/end date starts and ends at end of day ([cadbefb](https://github.com/FlyBodies/fly-fit/commit/cadbefb2abf71f596ff4f6ee86b86331c4f2ba98))
* Cleanup logout function,  modifying android intent configs ([5e42808](https://github.com/FlyBodies/fly-fit/commit/5e4280816a934579ead780081fafa8d22faf3316))
* Fixed semantic release repositoryUrl ([bf756cb](https://github.com/FlyBodies/fly-fit/commit/bf756cbbfe784c6f9bb567bf1249828e6e73306d))
* Remove the minimum date on the DateTimePicker component ([6c876cc](https://github.com/FlyBodies/fly-fit/commit/6c876cc5e691bc72338b1c4dd0ef0efc3cd25586))
* Revert target sdk version android ([1dc562e](https://github.com/FlyBodies/fly-fit/commit/1dc562eac3f857d212cc7f93e96e380cec31b05f))
* Small ignore fixes, dep upgrades, added react-native-health-connect ([5956604](https://github.com/FlyBodies/fly-fit/commit/59566043272f842012b9384c07116b6f18679f3e))

## [1.3.18](https://github.com/FlyBodies/fly-fit/compare/v1.3.17...v1.3.18) (2023-12-16)


### Bug Fixes

* Fix clean script cleans android builds ([f9b0495](https://github.com/FlyBodies/fly-fit/commit/f9b04953af1c79ecf4464502c33065d355f47540))

## [1.3.17](https://github.com/FlyBodies/fly-fit/compare/v1.3.16...v1.3.17) (2023-12-16)


### Bug Fixes

* Specified android sdk versions in app config ([220a5b8](https://github.com/FlyBodies/fly-fit/commit/220a5b892dc80f4c1ceb08b49c23eeb2158974b3))

## [1.3.16](https://github.com/FlyBodies/fly-fit/compare/v1.3.15...v1.3.16) (2023-12-16)


### Bug Fixes

* Allow submitting android builds in ci-cd script ([3cafe2c](https://github.com/FlyBodies/fly-fit/commit/3cafe2c166222407ad3725f7a6a85e4d88a1b0c1))

## [1.3.15](https://github.com/FlyBodies/fly-fit/compare/v1.3.14...v1.3.15) (2023-12-16)


### Bug Fixes

* Added build configs for android ([7bd8ca0](https://github.com/FlyBodies/fly-fit/commit/7bd8ca02d192951f2ca081eeed74e40eb3652135))
* Added import user scripts, fixed app crashing from bad phoneNumber user field ([#54](https://github.com/FlyBodies/fly-fit/issues/54)) ([269b15e](https://github.com/FlyBodies/fly-fit/commit/269b15eee834afdb97d1e8d79736b9fdb7491861))
* Android build config changes ([bad85d2](https://github.com/FlyBodies/fly-fit/commit/bad85d251b50c9fd223e5f92d55a3b270aa725e3))
* Cleaned up label of challenge start label on challenge card ([e349e12](https://github.com/FlyBodies/fly-fit/commit/e349e1243df523e36534a25153d90c443db5102c))
* Created more performant useDatastoreQuery hook ([1bc9388](https://github.com/FlyBodies/fly-fit/commit/1bc938802633c6e6f596058d9db226fafd7c1415))
* Improved import.runbook documentation ([0ef1d7f](https://github.com/FlyBodies/fly-fit/commit/0ef1d7f7b9156b0244385cb97daf5eef7013ee24))
* Small fixes to start supporting android build ([8e370b8](https://github.com/FlyBodies/fly-fit/commit/8e370b8fe5607f8570af48867c5f5ce417447847))
* Unflatten analytics log messages in functions backend ([de32307](https://github.com/FlyBodies/fly-fit/commit/de3230748a36012a30569aa2772fe11d1a855532))
* upgrade dependencies ([d80b579](https://github.com/FlyBodies/fly-fit/commit/d80b5791172d99d8f9a24192a6afdd6051c81692))

## [1.3.14](https://github.com/FlyBodies/fly-fit/compare/v1.3.13...v1.3.14) (2023-12-04)


### Bug Fixes

* Added preview channel for eas ([06697f6](https://github.com/FlyBodies/fly-fit/commit/06697f6d59b5655705a1af3358bee3d0bf2892dc))
* dev/prev simulator builds, added 100 to home screen ([5c883a0](https://github.com/FlyBodies/fly-fit/commit/5c883a00e9d20a49f9b44f54bc809af3f994e0e3))
* Update copyright label to include year ([45bcd70](https://github.com/FlyBodies/fly-fit/commit/45bcd70e52552cf868511096881f763a5020cb86))
* update package.json scripts ([fd65151](https://github.com/FlyBodies/fly-fit/commit/fd6515156248e785d14693e2e908f7c3f333635b))

## [1.3.13](https://github.com/FlyBodies/fly-fit/compare/v1.3.12...v1.3.13) (2023-12-03)


### Bug Fixes

* Login screen fixes, calculation of `isSameDay` fix, reload to loading screen ([#53](https://github.com/FlyBodies/fly-fit/issues/53)) ([f6c7709](https://github.com/FlyBodies/fly-fit/commit/f6c7709cc6437270da1681977a88e89650c994b0))

## [1.3.12](https://github.com/FlyBodies/fly-fit/compare/v1.3.11...v1.3.12) (2023-12-03)


### Bug Fixes

* Revert dev build script ([c6529e9](https://github.com/FlyBodies/fly-fit/commit/c6529e909ba98f22a41b8b3878ac2072f1e750dd))

## [1.3.11](https://github.com/FlyBodies/fly-fit/compare/v1.3.10...v1.3.11) (2023-12-03)


### Bug Fixes

* Performance improvements, better logs, improved testing ([#52](https://github.com/FlyBodies/fly-fit/issues/52)) ([09b994a](https://github.com/FlyBodies/fly-fit/commit/09b994a47e0afb2ef0b1e1d0f0370474aeb026f6))

## [1.3.10](https://github.com/FlyBodies/fly-fit/compare/v1.3.9...v1.3.10) (2023-12-02)


### Bug Fixes

* updated build scripts to account for optional deploying ([3cc4c70](https://github.com/FlyBodies/fly-fit/commit/3cc4c70ec94232085ae900f6d3c000589879f64f))
* updated dependencies ([fe81b0b](https://github.com/FlyBodies/fly-fit/commit/fe81b0bc46191e3261c34a5cadd7f571d65035b9))

## [1.3.9](https://github.com/FlyBodies/fly-fit/compare/v1.3.8...v1.3.9) (2023-11-26)


### Bug Fixes

* Removed the portal/dialog from the privacy policy confirmation ([d38301a](https://github.com/FlyBodies/fly-fit/commit/d38301a030e5b3d6fe6026d8795760d9f8191860))

## [1.3.8](https://github.com/FlyBodies/fly-fit/compare/v1.3.7...v1.3.8) (2023-11-26)


### Bug Fixes

* Remove simulator settings from eas development config ([445d335](https://github.com/FlyBodies/fly-fit/commit/445d33572f5c64b11e8fb571b927a6e630b63d47))

## [1.3.7](https://github.com/FlyBodies/fly-fit/compare/v1.3.6...v1.3.7) (2023-11-26)


### Bug Fixes

* Improved analytics cloud function grouping by level and error name ([b24d97e](https://github.com/FlyBodies/fly-fit/commit/b24d97ed1e80cdbf5fc27319cd3b98d9900c250e))

## [1.3.6](https://github.com/FlyBodies/fly-fit/compare/v1.3.5...v1.3.6) (2023-11-26)


### Bug Fixes

* Added remote logging to app for error and debug logs ([#51](https://github.com/FlyBodies/fly-fit/issues/51)) ([4d41b21](https://github.com/FlyBodies/fly-fit/commit/4d41b21c711b106b9dbe4d72207997ff07766597))
* Made all dev/prod operations synchronous and use new .env file ([38ef2b0](https://github.com/FlyBodies/fly-fit/commit/38ef2b05daacbc1556630baf43621d179a938e2c))

## [1.3.5](https://github.com/FlyBodies/fly-fit/compare/v1.3.4...v1.3.5) (2023-11-25)


### Bug Fixes

* Removed exit invalid js function, disable ts setup, remove dev sim ([332ffeb](https://github.com/FlyBodies/fly-fit/commit/332ffebb13f05eda31c972aee82f32f0e2be32e8))

## [1.3.4](https://github.com/FlyBodies/fly-fit/compare/v1.3.3...v1.3.4) (2023-11-25)


### Bug Fixes

* Fix outputting to .env files when running ci-cd script ([#50](https://github.com/FlyBodies/fly-fit/issues/50)) ([e07b318](https://github.com/FlyBodies/fly-fit/commit/e07b318ba6c90a5c0aaacc8a2f1cc4c777dac5b9))

## [1.3.3](https://github.com/FlyBodies/fly-fit/compare/v1.3.2...v1.3.3) (2023-11-24)


### Bug Fixes

* Remove delay code used when debugging ([#49](https://github.com/FlyBodies/fly-fit/issues/49)) ([46fa383](https://github.com/FlyBodies/fly-fit/commit/46fa383ddcaa037b8f2c474e5042ec9f916a7ff2))

## [1.3.2](https://github.com/FlyBodies/fly-fit/compare/v1.3.1...v1.3.2) (2023-11-24)


### Bug Fixes

* Add privacy policy confirmation page ([#48](https://github.com/FlyBodies/fly-fit/issues/48)) ([d2527c7](https://github.com/FlyBodies/fly-fit/commit/d2527c7ed6c789b3fc070c4c17d92e525bf7d5fd))
* Enhanced the calendar days to show progress ([#47](https://github.com/FlyBodies/fly-fit/issues/47)) ([efbb0f5](https://github.com/FlyBodies/fly-fit/commit/efbb0f5182f6615334daf60b71899477c8a81c82))

## [1.3.1](https://github.com/FlyBodies/fly-fit/compare/v1.3.0...v1.3.1) (2023-11-18)


### Bug Fixes

* Minor dependency updates ([#45](https://github.com/FlyBodies/fly-fit/issues/45)) ([b33f46b](https://github.com/FlyBodies/fly-fit/commit/b33f46b6abca83fbd7b4062b8a61a5f5075331d0))
* NPM strict versions for cloud functions ([#46](https://github.com/FlyBodies/fly-fit/issues/46)) ([b750664](https://github.com/FlyBodies/fly-fit/commit/b750664f07cfdd0cb9752718c3f807a13c34949a))

# [1.3.0](https://github.com/FlyBodies/fly-fit/compare/v1.2.0...v1.3.0) (2023-11-17)


### Bug Fixes

* Login email field lower case only ([#44](https://github.com/FlyBodies/fly-fit/issues/44)) ([52b1986](https://github.com/FlyBodies/fly-fit/commit/52b19864d97541b34f2cb7d14aaf0afc4b78b053))


### Features

* Added user account deletion, performance improvements, node 20 ([#43](https://github.com/FlyBodies/fly-fit/issues/43)) ([9459eeb](https://github.com/FlyBodies/fly-fit/commit/9459eeb0daaa54679cd18365483d1c72ba8b9631))

# [1.2.0](https://github.com/FlyBodies/fly-fit/compare/v1.1.3...v1.2.0) (2023-11-11)


### Features

* Added progress bar on home page ([#42](https://github.com/FlyBodies/fly-fit/issues/42)) ([4faec7b](https://github.com/FlyBodies/fly-fit/commit/4faec7b0a1f04612f47e93992e57abc0f4e13192))

## [1.1.3](https://github.com/FlyBodies/fly-fit/compare/v1.1.2...v1.1.3) (2023-11-08)


### Bug Fixes

* Added Firebase storage rules, updated challenge countdown values and labels ([#41](https://github.com/FlyBodies/fly-fit/issues/41)) ([2771329](https://github.com/FlyBodies/fly-fit/commit/27713294a58126a6cfa61b43fe4a2a5b110df6bf))

## [1.1.2](https://github.com/FlyBodies/fly-fit/compare/v1.1.1...v1.1.2) (2023-10-23)


### Bug Fixes

* Add special test users, and remove EULA and security policy ([#40](https://github.com/FlyBodies/fly-fit/issues/40)) ([1da6449](https://github.com/FlyBodies/fly-fit/commit/1da644947e11ecc1752b06d850af7512528ebb97))

## [1.1.1](https://github.com/FlyBodies/fly-fit/compare/v1.1.0...v1.1.1) (2023-10-17)


### Bug Fixes

* Improved login error handling, fixed edit profile initialState bug ([#38](https://github.com/FlyBodies/fly-fit/issues/38)) ([6c69ce9](https://github.com/FlyBodies/fly-fit/commit/6c69ce9a5e18cfd1529520afdf4427a8d8782647))
* Login email validation, login help actions, link legal documents ([#39](https://github.com/FlyBodies/fly-fit/issues/39)) ([ddac578](https://github.com/FlyBodies/fly-fit/commit/ddac578308c81130a417f143fefb1adeebd6721d))

# [1.1.0](https://github.com/FlyBodies/fly-fit/compare/v1.0.3...v1.1.0) (2023-10-16)


### Features

* Added profile page, edit profile, upload image, and view others profiles ([#37](https://github.com/FlyBodies/fly-fit/issues/37)) ([3a9e6b4](https://github.com/FlyBodies/fly-fit/commit/3a9e6b4db07b3ef90b20080c1e8d3a0e565704c3))

## [1.0.3](https://github.com/FlyBodies/fly-fit/compare/v1.0.2...v1.0.3) (2023-10-15)


### Bug Fixes

* Added edit workout feature, other small workout enhancements ([#36](https://github.com/FlyBodies/fly-fit/issues/36)) ([ffc7d2b](https://github.com/FlyBodies/fly-fit/commit/ffc7d2bd05ce8a852af80ea8985490d3c3688570))

## [1.0.2](https://github.com/FlyBodies/fly-fit/compare/v1.0.1...v1.0.2) (2023-10-14)


### Bug Fixes

* Made app version pull from package.json ([f370d03](https://github.com/FlyBodies/fly-fit/commit/f370d039cac7e637aea3bc136d5e541a1a1f431b))

## [1.0.1](https://github.com/FlyBodies/fly-fit/compare/v1.0.0...v1.0.1) (2023-10-14)


### Bug Fixes

* Added gh_token file to cicd script ([eaa8782](https://github.com/FlyBodies/fly-fit/commit/eaa87826045bf5eb2895cb188a2e0f675ff5d44b))
* Added package.json version command ([0584620](https://github.com/FlyBodies/fly-fit/commit/0584620ca5918e67efa5dcdba853f4de2015d47d))
* Functions tsconfig with patch updates ([d8d0298](https://github.com/FlyBodies/fly-fit/commit/d8d0298d2df7dd7802a7eeab1aceb314de086835))
* Revert npmrc change ([9143e3e](https://github.com/FlyBodies/fly-fit/commit/9143e3ee82c0428c927a05d6a264bc706ae65db2))

# 1.0.0 (2023-10-14)


### Features

* Added semantic release ([#35](https://github.com/FlyBodies/fly-fit/issues/35)) ([b879731](https://github.com/FlyBodies/fly-fit/commit/b879731d8274a90009d821a1903b8b1d233b66d3))
