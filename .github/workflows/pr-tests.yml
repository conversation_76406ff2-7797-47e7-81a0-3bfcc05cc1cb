name: PR Tests

on:
  pull_request:
    branches:
      - main

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    steps:
      - name: 🏗️ Checkout code
        uses: actions/checkout@v4

      - name: 🏗️ Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 22.14.0
          cache: npm
          cache-dependency-path: |
            package-lock.json
            functions/package-lock.json

      - name: 📦 Install dependencies (app)
        run: npm ci --ignore-scripts

      - name: 📦 Install dependencies (functions)
        run: cd functions && npm ci --ignore-scripts

      - name: 🛠️ Setup env (Dev)
        run: npm run env:local:dev

      - name: 🧪 Run tests
        run: npm run test:ci
